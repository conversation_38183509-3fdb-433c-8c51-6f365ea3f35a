#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
test_semantic_chunking.py

测试两阶段语义分块系统
"""

import logging
import tempfile
from pathlib import Path
from semantic_chunker import SemanticChunker
from chunk_processor import ChunkProcessor

def test_semantic_chunker():
    """测试语义分块器"""
    print("=== 测试语义分块器 ===")
    
    # 模拟AI服务
    class MockAIService:
        def __init__(self):
            self.config = {}
        
        def call_api(self, chunk, idx, cache_dir):
            # 模拟AI处理，返回成功
            return True
    
    ai_service = MockAIService()
    chunker = SemanticChunker(ai_service)
    
    # 测试内容
    test_content = """# 第一章 基础概念

这是第一章的介绍内容，包含基础概念的说明。

## 1.1 什么是人工智能？

人工智能（Artificial Intelligence，AI）是计算机科学的一个分支，它企图了解智能的实质，并生产出一种新的能以人类智能相似的方式做出反应的智能机器。

### 问题1
人工智能的定义是什么？

人工智能是计算机科学的一个分支，旨在创造能够模拟人类智能行为的机器和系统。

## 1.2 机器学习基础

机器学习是人工智能的一个重要分支，它使计算机能够在没有明确编程的情况下学习。

### 问题2
什么是机器学习？

机器学习是一种人工智能技术，它使计算机系统能够从数据中自动学习和改进，而无需明确编程。

# 第二章 深度学习

深度学习是机器学习的一个子集，它模仿人脑的工作方式来处理数据。

## 2.1 神经网络

神经网络是深度学习的基础，它由多个相互连接的节点组成。

### 问题3
神经网络的基本组成是什么？

神经网络由输入层、隐藏层和输出层组成，每层包含多个神经元节点。

## 2.2 卷积神经网络

卷积神经网络（CNN）特别适用于图像处理任务。

### 问题4
CNN的主要特点是什么？

CNN使用卷积层来提取图像特征，具有平移不变性和参数共享的特点。

# 第三章 应用实例

本章介绍人工智能在各个领域的应用实例。

## 3.1 计算机视觉

计算机视觉使计算机能够理解和解释视觉信息。

### 问题5
计算机视觉的主要应用有哪些？

计算机视觉广泛应用于图像识别、目标检测、人脸识别、医学影像分析等领域。

# 第四章 自然语言处理

自然语言处理（NLP）是人工智能的重要分支，专注于让计算机理解和生成人类语言。

## 4.1 文本分析

文本分析是NLP的基础任务，包括分词、词性标注、命名实体识别等。

### 问题6
什么是分词？

分词是将连续的文本分割成有意义的词汇单元的过程，是中文NLP的重要预处理步骤。

## 4.2 语言模型

语言模型用于预测文本序列的概率分布。

### 问题7
什么是Transformer模型？

Transformer是一种基于注意力机制的神经网络架构，在自然语言处理任务中表现出色。

# 第五章 强化学习

强化学习是机器学习的一个重要分支，通过与环境交互来学习最优策略。

## 5.1 基本概念

强化学习包含智能体、环境、状态、动作、奖励等基本要素。

### 问题8
强化学习的核心要素有哪些？

强化学习的核心要素包括智能体（Agent）、环境（Environment）、状态（State）、动作（Action）和奖励（Reward）。

## 5.2 Q学习

Q学习是一种经典的强化学习算法。

### 问题9
Q学习的基本原理是什么？

Q学习通过学习状态-动作价值函数Q(s,a)来找到最优策略，使用贝尔曼方程进行迭代更新。
""" * 3  # 重复内容使其足够长

    # 执行语义分块
    result = chunker.preprocess_document(test_content, "test_project")
    
    print(f"分块结果:")
    print(f"  成功: {result.success}")
    print(f"  原始长度: {result.original_length:,} 字符")
    print(f"  处理后长度: {result.preprocessed_length:,} 字符")
    print(f"  分块数量: {result.chunk_count}")
    print(f"  处理时间: {result.processing_time:.2f}s")
    
    if result.error_message:
        print(f"  错误信息: {result.error_message}")
    
    return result.preprocessed_content if result.success else test_content

def test_chunk_processor(preprocessed_content):
    """测试分块处理器"""
    print("\n=== 测试分块处理器 ===")
    
    # 模拟AI服务
    class MockAIService:
        def __init__(self):
            self.config = {}
            self.channels = []
        
        def call_api(self, chunk, idx, cache_dir):
            # 模拟AI处理时间
            import time
            time.sleep(0.05)
            return True
    
    ai_service = MockAIService()
    processor = ChunkProcessor(ai_service)
    
    # 测试分割
    chunks = processor.split_by_markers(preprocessed_content)
    print(f"分割结果: {len(chunks)} 个语义块")
    
    # 测试统计
    stats = processor.get_chunk_statistics(preprocessed_content)
    print(f"\n统计信息:")
    print(f"  总内容长度: {stats['total_content_length']:,} 字符")
    print(f"  分块标记数量: {stats['chunk_marker_count']}")
    print(f"  有效语义块: {stats['effective_chunks']}")
    
    validation = stats['validation']
    print(f"\n质量验证:")
    print(f"  验证状态: {'✅ 通过' if validation['valid'] else '❌ 失败'}")
    print(f"  验证信息: {validation['reason']}")
    print(f"  平均块长度: {validation['avg_length']:.0f} 字符")
    print(f"  最短块: {validation['min_length']} 字符")
    print(f"  最长块: {validation['max_length']} 字符")
    
    dist = validation['length_distribution']
    print(f"\n长度分布:")
    print(f"  短块 (<1000字符): {dist['short']}")
    print(f"  中等块 (1000-5000字符): {dist['medium']}")
    print(f"  长块 (>5000字符): {dist['long']}")
    
    # 测试预览
    previews = processor.preview_chunks(preprocessed_content, 100)
    print(f"\n分块预览:")
    for i, preview in enumerate(previews[:3]):  # 只显示前3个
        print(f"  块 {preview['index'] + 1}: {preview['length']}字符, {preview['lines']}行")
        print(f"    预览: {preview['preview'][:80]}...")
    
    if len(previews) > 3:
        print(f"  ... 还有 {len(previews) - 3} 个块")
    
    # 测试处理（使用临时缓存目录）
    with tempfile.TemporaryDirectory() as temp_dir:
        project_config = {
            'CACHE_DIR': temp_dir
        }
        
        print(f"\n开始处理语义分块...")
        processing_result = processor.process_semantic_chunks(
            preprocessed_content,
            "test_project",
            project_config
        )
        
        print(f"处理结果:")
        print(f"  成功: {processing_result.success}")
        print(f"  总块数: {processing_result.total_chunks}")
        print(f"  已处理: {processing_result.processed_chunks}")
        print(f"  失败数: {processing_result.failed_chunks}")
        print(f"  处理时间: {processing_result.processing_time:.2f}s")
        
        if processing_result.error_message:
            print(f"  错误信息: {processing_result.error_message}")

def test_file_management():
    """测试文件管理功能"""
    print("\n=== 测试文件管理功能 ===")
    
    # 模拟AI服务
    class MockAIService:
        def __init__(self):
            self.config = {}
        
        def call_api(self, chunk, idx, cache_dir):
            return True
    
    ai_service = MockAIService()
    chunker = SemanticChunker(ai_service)
    
    # 创建临时目录结构
    with tempfile.TemporaryDirectory() as temp_dir:
        base_dir = Path(temp_dir)
        project_name = "test_project_file_mgmt"
        
        # 创建项目目录和原始markdown文件
        project_dir = base_dir / "extracted" / project_name
        project_dir.mkdir(parents=True)
        
        original_md = project_dir / f"{project_name}.md"
        test_content = "# 测试内容\n\n这是测试内容。"
        
        with open(original_md, 'w', encoding='utf-8') as f:
            f.write(test_content)
        
        print(f"创建测试项目: {project_name}")
        print(f"原始文件: {original_md}")
        
        # 测试获取预处理文件路径
        preprocessed_path = chunker.get_preprocessed_file_path(project_name, base_dir)
        print(f"预处理文件路径: {preprocessed_path}")
        
        # 测试保存预处理内容
        preprocessed_content = test_content + "\n\n<!-- CHUNK_BREAK -->\n\n# 第二部分\n\n更多内容。"
        success = chunker.save_preprocessed_content(preprocessed_content, preprocessed_path)
        print(f"保存预处理内容: {'✅ 成功' if success else '❌ 失败'}")
        
        # 测试加载预处理内容
        loaded_content = chunker.load_preprocessed_content(preprocessed_path)
        print(f"加载预处理内容: {'✅ 成功' if loaded_content else '❌ 失败'}")
        
        if loaded_content:
            print(f"内容长度: {len(loaded_content)} 字符")
            print(f"内容匹配: {'✅ 是' if loaded_content == preprocessed_content else '❌ 否'}")
        
        # 测试文件有效性检查
        import time
        time.sleep(0.1)  # 确保时间差
        
        # 更新原始文件
        with open(original_md, 'w', encoding='utf-8') as f:
            f.write(test_content + "\n\n# 新增内容")
        
        is_valid = chunker.is_preprocessed_file_valid(preprocessed_path, original_md)
        print(f"预处理文件有效性: {'✅ 有效' if is_valid else '❌ 无效（需要重新处理）'}")

def test_integration():
    """测试集成功能"""
    print("\n=== 测试集成功能 ===")
    
    # 模拟完整的两阶段处理流程
    class MockAIService:
        def __init__(self):
            self.config = {}
            self.channels = []
        
        def call_api(self, chunk, idx, cache_dir):
            import time
            time.sleep(0.02)  # 模拟处理时间
            return True
    
    ai_service = MockAIService()
    chunker = SemanticChunker(ai_service)
    processor = ChunkProcessor(ai_service)
    
    # 测试内容
    test_content = """# AI学习指南

## 第一部分：基础知识

### 问题1：什么是人工智能？
人工智能是计算机科学的一个分支。

### 问题2：AI的应用领域有哪些？
AI应用于图像识别、自然语言处理、机器人等领域。

## 第二部分：技术细节

### 问题3：什么是机器学习？
机器学习是AI的一个重要分支。

### 问题4：深度学习的特点是什么？
深度学习使用多层神经网络。""" * 5  # 重复内容
    
    print("开始完整的两阶段处理流程...")
    
    # Stage 1: 语义分块
    print("\n🔄 Stage 1: 语义分块预处理")
    chunking_result = chunker.preprocess_document(test_content, "integration_test")
    
    if chunking_result.success:
        print(f"✅ Stage 1完成: {chunking_result.chunk_count}个语义块")
        
        # Stage 2: 处理语义分块
        print("\n🔄 Stage 2: 处理语义分块")
        
        with tempfile.TemporaryDirectory() as temp_dir:
            project_config = {'CACHE_DIR': temp_dir}
            
            processing_result = processor.process_semantic_chunks(
                chunking_result.preprocessed_content,
                "integration_test",
                project_config
            )
            
            if processing_result.success:
                print(f"✅ Stage 2完成: 处理了{processing_result.processed_chunks}个块")
                print(f"总处理时间: {chunking_result.processing_time + processing_result.processing_time:.2f}s")
            else:
                print(f"❌ Stage 2失败: {processing_result.error_message}")
    else:
        print(f"❌ Stage 1失败: {chunking_result.error_message}")

if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
    
    print("🧪 两阶段语义分块系统测试")
    print("=" * 50)
    
    # 测试语义分块器
    preprocessed_content = test_semantic_chunker()
    
    # 测试分块处理器
    test_chunk_processor(preprocessed_content)
    
    # 测试文件管理
    test_file_management()
    
    # 测试集成功能
    test_integration()
    
    print("\n🎉 所有测试完成！")
