#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
test_real_ai_chunking.py

测试真实的AI语义分块功能
"""

import logging
from ai_service import AIService
from semantic_chunker import SemanticChunker
from multi_channel_config import MultiChannelConfigParser

def test_real_ai_chunking():
    """测试真实的AI语义分块功能"""
    logging.basicConfig(level=logging.INFO)
    
    # 初始化AI服务
    try:
        config_parser = MultiChannelConfigParser()
        config = config_parser.get_config()
        
        ai_service = AIService(config)
        
        # 创建语义分块器
        chunker = SemanticChunker(ai_service, config=config)
        
        # 创建足够长的测试内容（超过6000字符）
        test_content = """# 人工智能完整学习指南

## 第一章：人工智能基础概念

### 1.1 什么是人工智能？

人工智能（Artificial Intelligence，AI）是计算机科学的一个分支，它企图了解智能的实质，并生产出一种新的能以人类智能相似的方式做出反应的智能机器。人工智能研究的一个主要目标是使机器能够胜任一些通常需要人类智能才能完成的复杂工作。

人工智能的发展历程可以追溯到20世纪50年代。1950年，英国数学家阿兰·图灵提出了著名的"图灵测试"，这被认为是人工智能领域的开端。图灵测试的基本思想是：如果一台机器能够与人类进行对话而不被识破，那么这台机器就具有了智能。

### 问题1：人工智能的定义包含哪些关键要素？

人工智能的定义包含以下关键要素：
1. 模拟人类智能：AI系统试图复制或模拟人类的认知能力
2. 自主决策：能够在没有明确编程指令的情况下做出决策
3. 学习能力：能够从经验中学习并改进性能
4. 适应性：能够适应新的环境和任务
5. 问题解决：能够解决复杂的问题和完成困难的任务

### 1.2 人工智能的分类

人工智能可以从不同的角度进行分类。按照能力水平，可以分为：

**弱人工智能（Narrow AI）**：专门设计用于执行特定任务的AI系统，如语音识别、图像识别等。目前我们使用的大多数AI系统都属于这一类。

**强人工智能（General AI）**：具有与人类相当的智能水平，能够理解、学习和应用知识到各种不同领域的AI系统。这种AI目前还不存在。

**超人工智能（Super AI）**：在所有领域都超越人类智能的AI系统。这是一个理论概念，目前还远未实现。

### 问题2：弱人工智能和强人工智能的主要区别是什么？

弱人工智能和强人工智能的主要区别在于：
- 应用范围：弱AI专注于特定任务，强AI能处理各种任务
- 智能水平：弱AI在特定领域可能超越人类，但缺乏通用智能
- 学习能力：强AI具有更强的迁移学习和泛化能力
- 自主性：强AI具有更高的自主决策和创造能力
- 发展阶段：弱AI已经实现并广泛应用，强AI仍在研究阶段

## 第二章：机器学习基础

### 2.1 机器学习概述

机器学习是人工智能的一个重要分支，它使计算机能够在没有明确编程的情况下学习。机器学习算法通过分析大量数据来识别模式，并使用这些模式对新数据进行预测或决策。

机器学习的核心思想是让计算机从数据中自动学习规律，而不是通过人工编程来实现特定功能。这种方法特别适用于那些难以用传统编程方法解决的复杂问题。

### 2.2 机器学习的类型

机器学习主要分为三种类型：

**监督学习（Supervised Learning）**：使用标记数据进行训练，目标是学习输入和输出之间的映射关系。常见的监督学习任务包括分类和回归。

**无监督学习（Unsupervised Learning）**：使用未标记的数据，目标是发现数据中的隐藏模式或结构。常见的无监督学习任务包括聚类和降维。

**强化学习（Reinforcement Learning）**：通过与环境交互来学习，通过试错和奖励机制来优化行为策略。

### 问题3：监督学习和无监督学习的主要应用场景有哪些？

监督学习的主要应用场景：
- 图像分类：识别图片中的物体或场景
- 垃圾邮件检测：判断邮件是否为垃圾邮件
- 医疗诊断：根据症状和检查结果预测疾病
- 股价预测：基于历史数据预测未来价格走势
- 语音识别：将语音转换为文字

无监督学习的主要应用场景：
- 客户细分：根据购买行为将客户分组
- 异常检测：识别网络安全威胁或设备故障
- 推荐系统：发现用户偏好模式
- 数据压缩：降低数据维度同时保持重要信息
- 市场篮子分析：发现商品之间的关联关系

## 第三章：深度学习

### 3.1 神经网络基础

神经网络是深度学习的基础，它模仿人脑神经元的工作方式。一个基本的神经网络由输入层、隐藏层和输出层组成。每个神经元接收输入信号，经过加权求和和激活函数处理后产生输出。

深度学习使用多层神经网络（通常超过3层）来学习数据的复杂表示。通过增加网络深度，模型能够学习更抽象和高级的特征。

### 3.2 卷积神经网络（CNN）

卷积神经网络特别适用于处理图像数据。CNN的主要特点包括：
- 局部连接：每个神经元只与局部区域连接
- 权重共享：相同的滤波器在整个图像上使用
- 平移不变性：对图像的平移具有鲁棒性

### 问题4：卷积神经网络为什么特别适合图像处理？

卷积神经网络特别适合图像处理的原因：
1. 局部特征提取：卷积操作能够有效提取图像的局部特征
2. 参数共享：减少了模型参数数量，提高了训练效率
3. 平移不变性：无论物体在图像中的位置如何，都能被识别
4. 层次化特征学习：从低级边缘特征到高级语义特征
5. 池化操作：降低计算复杂度并提供一定的旋转和缩放不变性

### 3.3 循环神经网络（RNN）

循环神经网络专门用于处理序列数据，如文本、语音和时间序列。RNN的特点是具有记忆能力，能够利用之前的信息来处理当前的输入。

长短期记忆网络（LSTM）和门控循环单元（GRU）是RNN的改进版本，能够更好地处理长序列数据。

### 问题5：RNN相比传统神经网络有什么优势？

RNN相比传统神经网络的优势：
- 序列建模能力：能够处理变长的序列数据
- 记忆机制：具有短期记忆，能够利用历史信息
- 参数共享：在时间步之间共享参数，提高效率
- 动态处理：能够实时处理流式数据
- 上下文理解：能够理解序列中的上下文关系

## 第四章：自然语言处理

### 4.1 NLP基础

自然语言处理（Natural Language Processing，NLP）是人工智能的重要分支，专注于让计算机理解、解释和生成人类语言。NLP结合了计算机科学、人工智能和语言学的知识。

NLP的主要任务包括：
- 文本分类
- 情感分析
- 命名实体识别
- 机器翻译
- 问答系统
- 文本摘要

### 4.2 Transformer架构

Transformer是近年来NLP领域最重要的突破之一。它完全基于注意力机制，摒弃了传统的循环和卷积结构。Transformer的核心是自注意力机制，能够并行处理序列中的所有位置。

### 问题6：Transformer相比RNN有哪些优势？

Transformer相比RNN的优势：
- 并行计算：能够并行处理序列中的所有位置
- 长距离依赖：更好地捕获长距离的依赖关系
- 训练效率：训练速度更快，更容易并行化
- 可解释性：注意力权重提供了模型决策的可解释性
- 迁移学习：预训练模型能够很好地迁移到下游任务

## 第五章：计算机视觉

### 5.1 图像处理基础

计算机视觉是让计算机能够理解和解释视觉信息的技术。它涉及图像获取、处理、分析和理解的整个过程。

主要任务包括：
- 图像分类
- 目标检测
- 语义分割
- 实例分割
- 人脸识别
- 光学字符识别（OCR）

### 问题7：目标检测和图像分类的区别是什么？

目标检测和图像分类的区别：
- 任务目标：分类识别整张图片的类别，检测定位图片中的多个物体
- 输出形式：分类输出类别标签，检测输出边界框和类别
- 复杂度：检测比分类更复杂，需要同时解决定位和分类问题
- 应用场景：分类用于图片标注，检测用于自动驾驶、安防监控等
- 评估指标：分类使用准确率，检测使用mAP等指标

## 第六章：强化学习

### 6.1 强化学习基础

强化学习是机器学习的一个重要分支，它通过与环境交互来学习最优策略。强化学习的核心要素包括：
- 智能体（Agent）
- 环境（Environment）
- 状态（State）
- 动作（Action）
- 奖励（Reward）

### 6.2 Q学习算法

Q学习是一种经典的强化学习算法，它通过学习状态-动作价值函数Q(s,a)来找到最优策略。Q学习使用贝尔曼方程进行迭代更新。

### 问题8：强化学习适用于哪些场景？

强化学习适用的场景：
- 游戏AI：如围棋、象棋、电子游戏
- 机器人控制：路径规划、动作控制
- 自动驾驶：决策规划、路径优化
- 推荐系统：个性化推荐策略
- 金融交易：投资策略优化
- 资源调度：云计算资源分配

## 总结

人工智能是一个快速发展的领域，涵盖了机器学习、深度学习、自然语言处理、计算机视觉和强化学习等多个子领域。每个领域都有其独特的方法和应用场景。

随着技术的不断进步，人工智能正在改变我们的生活和工作方式。未来，我们需要在享受AI带来便利的同时，也要关注其潜在的风险和挑战，确保AI技术的发展能够造福全人类。
""" * 5  # 重复内容使其足够长（增加到5倍）
        
        print(f"测试内容长度: {len(test_content)} 字符")

        # 如果内容不够长，再次扩展
        if len(test_content) < 15000:
            print("内容长度不足，进行扩展...")
            additional_content = """
## 附加章节：AI伦理与安全

### AI伦理的重要性

人工智能的快速发展带来了许多伦理问题。我们需要确保AI系统的公平性、透明性和可解释性。

### 问题：AI偏见如何产生？

AI偏见主要来源于：
1. 训练数据的偏见
2. 算法设计的偏见
3. 应用场景的偏见
4. 评估标准的偏见

### AI安全挑战

AI安全面临的主要挑战包括：
- 对抗性攻击
- 数据隐私保护
- 模型鲁棒性
- 系统可靠性

### 问题：如何确保AI系统的安全性？

确保AI系统安全性的方法：
1. 严格的测试和验证
2. 多层次的安全防护
3. 持续的监控和更新
4. 建立安全标准和规范
5. 加强国际合作

## 附加章节：AI在各行业的应用

### 医疗健康

AI在医疗领域的应用：
- 医学影像诊断
- 药物发现
- 个性化治疗
- 健康监测
- 手术机器人

### 金融服务

AI在金融领域的应用：
- 风险评估
- 欺诈检测
- 算法交易
- 智能投顾
- 信用评分

### 教育领域

AI在教育领域的应用：
- 个性化学习
- 智能辅导
- 自动评分
- 学习分析
- 虚拟教师

### 制造业

AI在制造业的应用：
- 预测性维护
- 质量控制
- 供应链优化
- 智能制造
- 机器人自动化

### 交通运输

AI在交通领域的应用：
- 自动驾驶
- 交通优化
- 路径规划
- 智能信号灯
- 车辆调度

## 附加章节：AI技术发展趋势

### 大模型时代

大规模预训练模型正在改变AI的发展格局：
- GPT系列模型
- BERT和其变体
- 多模态大模型
- 专业领域大模型

### 边缘AI

边缘计算与AI的结合：
- 降低延迟
- 保护隐私
- 减少带宽
- 提高可靠性

### 联邦学习

联邦学习的优势：
- 数据隐私保护
- 分布式训练
- 降低通信成本
- 提高模型泛化能力

### 可解释AI

可解释AI的重要性：
- 增强用户信任
- 满足监管要求
- 改进模型性能
- 发现潜在问题

### 量子机器学习

量子计算与机器学习的结合：
- 量子优势
- 新的算法范式
- 计算复杂度突破
- 未来发展前景
""" * 3  # 添加更多内容
            test_content += additional_content
            print(f"扩展后内容长度: {len(test_content)} 字符")
        
        # 执行语义分块
        print("开始AI语义分块...")
        result = chunker.preprocess_document(test_content, "real_ai_test")
        
        print(f"\n分块结果:")
        print(f"成功: {result.success}")
        print(f"原始长度: {result.original_length:,} 字符")
        print(f"处理后长度: {result.preprocessed_length:,} 字符")
        print(f"分块数量: {result.chunk_count}")
        print(f"处理时间: {result.processing_time:.2f}s")
        
        if result.error_message:
            print(f"错误信息: {result.error_message}")
        
        if result.chunk_count > 1:
            print(f"\n分块标记位置:")
            lines = result.preprocessed_content.split('\n')
            for i, line in enumerate(lines):
                if chunker.chunk_marker in line:
                    print(f"  第 {i+1} 行: {line}")
                    # 显示前后几行的上下文
                    start = max(0, i-3)
                    end = min(len(lines), i+4)
                    print(f"  上下文:")
                    for j in range(start, end):
                        prefix = ">>> " if j == i else "    "
                        print(f"  {prefix}{j+1}: {lines[j]}")
                    print()
        
        # 保存结果到文件以便检查
        if result.success and result.chunk_count > 1:
            output_file = "ai_chunking_result.md"
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(result.preprocessed_content)
            print(f"\n结果已保存到: {output_file}")
        
        return result.success
        
    except Exception as e:
        print(f"测试失败: {e}")
        return False

if __name__ == "__main__":
    success = test_real_ai_chunking()
    if success:
        print("\n✅ AI语义分块测试完成")
    else:
        print("\n❌ AI语义分块测试失败")
