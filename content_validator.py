#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
content_validator.py

AI内容验证器 - 实现字符数验证和重试决策逻辑
提供可配置的内容质量验证机制，确保AI生成内容的完整性
"""

import logging
from typing import Dict, Any, Optional
from dataclasses import dataclass


@dataclass
class ValidationResult:
    """验证结果数据类"""
    is_valid: bool
    input_length: int
    output_length: int
    ratio: float
    threshold: float
    reason: str = ""


class ContentValidator:
    """
    AI内容验证器
    
    实现基于字符数的内容验证，支持可配置的阈值和重试逻辑
    """
    
    def __init__(self, threshold: float = 0.8, max_retries: int = 2, enable_validation: bool = True):
        """
        初始化内容验证器
        
        Args:
            threshold: 最小字符数比例阈值 (0.0-1.0)
            max_retries: 最大重试次数
            enable_validation: 是否启用验证
        """
        self.threshold = max(0.0, min(1.0, threshold))  # 确保阈值在有效范围内
        self.max_retries = max(0, max_retries)
        self.enable_validation = enable_validation
        
        logging.info(f"内容验证器初始化: 阈值={self.threshold:.1%}, 最大重试={self.max_retries}, 启用={self.enable_validation}")
    
    def validate_content_length(self, input_text: str, output_text: str) -> ValidationResult:
        """
        验证输出内容长度是否符合要求
        
        Args:
            input_text: 输入文本
            output_text: AI生成的输出文本
            
        Returns:
            ValidationResult: 验证结果
        """
        if not self.enable_validation:
            return ValidationResult(
                is_valid=True,
                input_length=len(input_text),
                output_length=len(output_text),
                ratio=1.0,
                threshold=self.threshold,
                reason="验证已禁用"
            )
        
        input_length = len(input_text.strip())
        output_length = len(output_text.strip())
        
        # 处理空输入的情况
        if input_length == 0:
            return ValidationResult(
                is_valid=output_length == 0,
                input_length=input_length,
                output_length=output_length,
                ratio=0.0,
                threshold=self.threshold,
                reason="输入文本为空" if input_length == 0 else "输入为空但输出不为空"
            )
        
        # 计算长度比例
        ratio = output_length / input_length
        is_valid = ratio >= self.threshold
        
        reason = ""
        if not is_valid:
            reason = f"输出长度不足: {ratio:.1%} < {self.threshold:.1%}"
        else:
            reason = f"验证通过: {ratio:.1%} >= {self.threshold:.1%}"
        
        return ValidationResult(
            is_valid=is_valid,
            input_length=input_length,
            output_length=output_length,
            ratio=ratio,
            threshold=self.threshold,
            reason=reason
        )
    
    def should_retry(self, attempt_count: int) -> bool:
        """
        判断是否应该重试
        
        Args:
            attempt_count: 当前尝试次数 (从0开始)
            
        Returns:
            bool: 是否应该重试
        """
        if not self.enable_validation:
            return False
            
        return attempt_count < self.max_retries
    
    def get_retry_info(self, attempt_count: int) -> Dict[str, Any]:
        """
        获取重试信息
        
        Args:
            attempt_count: 当前尝试次数
            
        Returns:
            Dict: 重试信息
        """
        return {
            "current_attempt": attempt_count + 1,
            "max_attempts": self.max_retries + 1,  # 包括初始尝试
            "remaining_retries": max(0, self.max_retries - attempt_count),
            "can_retry": self.should_retry(attempt_count)
        }
    
    def validate_questions(self, questions: list, min_questions: int = 1) -> bool:
        """
        验证提取的问题数量是否符合要求
        
        Args:
            questions: 提取的问题列表
            min_questions: 最少问题数量
            
        Returns:
            bool: 是否符合要求
        """
        if not self.enable_validation:
            return True
            
        return len(questions) >= min_questions
    
    def get_validation_stats(self) -> Dict[str, Any]:
        """
        获取验证器统计信息
        
        Returns:
            Dict: 统计信息
        """
        return {
            "threshold": self.threshold,
            "max_retries": self.max_retries,
            "enable_validation": self.enable_validation,
            "threshold_percentage": f"{self.threshold:.1%}"
        }
    
    @classmethod
    def from_config(cls, config: Dict[str, Any]) -> 'ContentValidator':
        """
        从配置创建验证器实例
        
        Args:
            config: 配置字典
            
        Returns:
            ContentValidator: 验证器实例
        """
        threshold = float(config.get('CONTENT_VALIDATION_THRESHOLD', 0.8))
        max_retries = int(config.get('MAX_CONTENT_RETRIES', 2))
        enable_validation = config.get('ENABLE_CONTENT_VALIDATION', 'true').lower() == 'true'
        
        return cls(threshold=threshold, max_retries=max_retries, enable_validation=enable_validation)


def create_validator_from_config(config: Dict[str, Any]) -> ContentValidator:
    """
    便捷函数：从配置创建验证器
    
    Args:
        config: 配置字典
        
    Returns:
        ContentValidator: 验证器实例
    """
    return ContentValidator.from_config(config)


if __name__ == "__main__":
    # 测试代码
    logging.basicConfig(level=logging.INFO)
    
    # 创建验证器
    validator = ContentValidator(threshold=0.8, max_retries=2)
    
    # 测试验证
    input_text = "这是一个测试输入文本，包含一些内容用于验证。" * 10
    output_text = "这是AI生成的输出文本。" * 5
    
    result = validator.validate_content_length(input_text, output_text)
    print(f"验证结果: {result}")
    
    # 测试重试逻辑
    for attempt in range(4):
        retry_info = validator.get_retry_info(attempt)
        print(f"尝试 {attempt}: {retry_info}")
