#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
semantic_chunker.py

语义分块器 - 实现AI驱动的智能内容分块
Stage 1: AI预处理，在源Markdown内容中插入语义分块标记
"""

import logging
import time
from pathlib import Path
from typing import Dict, Any, Optional, List
from dataclasses import dataclass


@dataclass
class ChunkingResult:
    """分块结果数据类"""
    success: bool
    preprocessed_content: str
    chunk_count: int
    processing_time: float
    error_message: str = ""
    original_length: int = 0
    preprocessed_length: int = 0


class SemanticChunker:
    """
    语义分块器
    
    使用AI分析文档内容，在语义边界处插入分块标记
    """
    
    def __init__(self, ai_service, chunk_marker: str = "<!-- CHUNK_BREAK -->", config: dict = None):
        """
        初始化语义分块器

        Args:
            ai_service: AI服务实例
            chunk_marker: 分块标记字符串
            config: 配置字典
        """
        self.ai_service = ai_service
        self.chunk_marker = chunk_marker
        self.config = config or {}

        # AI分块配置参数
        self.enable_ai_chunking = self.config.get('ENABLE_AI_CHUNKING', 'true').lower() == 'true'
        # 目标块大小，我们期望每个分块大约这么大
        self.ai_chunking_target_size = int(self.config.get('AI_CHUNKING_TARGET_SIZE', 12000))
        # 发送给AI进行"智能剪刀"操作的上下文窗口大小
        self.ai_chunking_context_window_size = int(self.config.get('AI_CHUNKING_CONTEXT_WINDOW_SIZE', 3000))
        self.ai_chunking_timeout = int(self.config.get('AI_CHUNKING_TIMEOUT', 180))
        self.ai_chunking_fallback_to_rules = self.config.get('AI_CHUNKING_FALLBACK_TO_RULES', 'true').lower() == 'true'
        
        # <<< 新增：智能剪刀提示词 >>>
        self.smart_clipping_system_prompt = """# 角色
你是一个文本智能分块顾问。你的任务是检查一小段文本片段，并在其中找到**唯一一个最合适**的分割点。

# 我的工作流程 (为你提供上下文)
1.  我正在处理一个非常巨大的文本文件，里面包含许多“问答块”。
2.  我无法一次性处理整个文件。我会用一个固定的长度来预设一个分割点。
3.  为了避免在问题或答案中间切断，我会从预设分割点前后截取一段文本（即你现在收到的内容）发送给你。
4.  你的任务就是在这段文本中，找到那个最符合逻辑、最“干净”的边界，并用 `<!-- CHUNK_BREAK -->` 标记出来。

# 你的核心任务
分析我提供的文本片段，并在其中插入**一个且仅一个** `<!-- CHUNK_BREAK -->` 标记。

# 如何选择最佳分割点
1.  **识别边界信号**：一个“问答块”的结束标志是其被高亮标记的正确答案（例如：`<span style="color:rgb(255, 0, 0)"><strong>...</strong></span>`）。下一个“问答块”的开始标志是题号（如 `3.`）、引导性文字或图片。最佳分割点就在这两者之间。
2.  **选择唯一位置**：
    *   在给定的文本片段中，可能会有多个潜在的分割点。
    *   你应该选择**最接近文本片段中心位置**的那个分割点。这能最好地满足我初步设定的分块大小。
    *   如果整个片段只有一个清晰的分割点，那就选择那一个。
3.  **最终输出**：返回给我带有**一个** `<!-- CHUNK_BREAK -->` 标记的文本。
4.  **无合适分割点的情况**：如果在提供的文本片段中找不到任何一个完整的“问答块”边界（例如，片段的开头和结尾都在一个问题的内部），请直接返回原始文本，不要添加任何标记。
"""
        self.smart_clipping_task_prompt = """# 执行任务
请根据以上所有规则，处理我接下来提供的文本片段，找到唯一最佳的分割点并插入 `<!-- CHUNK_BREAK -->`。

---
{content}
---
"""

        chunking_mode = "AI驱动" if self.enable_ai_chunking else "规则驱动"
        logging.info(f"语义分块器初始化完成，模式: {chunking_mode}, 分块标记: {self.chunk_marker}")
        logging.info(f"AI分块配置: 目标大小={self.ai_chunking_target_size}, 上下文窗口={self.ai_chunking_context_window_size}, 超时={self.ai_chunking_timeout}s")

    def preprocess_document(self, content: str, project_name: str) -> ChunkingResult:
        """
        预处理文档，插入语义分块标记
        """
        start_time = time.time()
        original_length = len(content)

        logging.info(f"开始语义分块预处理: 项目={project_name}, 原始长度={original_length}字符")

        if original_length == 0:
            return ChunkingResult(success=False, preprocessed_content="", chunk_count=0, processing_time=0, error_message="输入内容为空")

        # 如果内容本身就小于目标块大小，不需要分块
        if original_length < self.ai_chunking_target_size * 1.2: # 增加一个buffer
            logging.info(f"内容较短({original_length}字符)，跳过语义分块")
            return ChunkingResult(
                success=True, preprocessed_content=content, chunk_count=1, processing_time=time.time() - start_time,
                original_length=original_length, preprocessed_length=original_length)

        try:
            if self.enable_ai_chunking:
                logging.info("使用AI驱动的智能语义分块 (滑动窗口策略)")
                preprocessed_content = self._ai_driven_semantic_chunking(content, project_name)
            else:
                logging.info("使用规则驱动的语义分块")
                preprocessed_content = self._rule_based_semantic_chunking(content, project_name)

            chunk_count = self._count_chunks(preprocessed_content)
            processing_time = time.time() - start_time
            preprocessed_length = len(preprocessed_content)

            logging.info(f"语义分块完成: 生成{chunk_count}个块, 处理时间{processing_time:.2f}s")
            return ChunkingResult(
                success=True, preprocessed_content=preprocessed_content, chunk_count=chunk_count,
                processing_time=processing_time, original_length=original_length, preprocessed_length=preprocessed_length)

        except Exception as e:
            error_msg = f"语义分块处理异常: {str(e)}"
            logging.error(error_msg, exc_info=True)
            return ChunkingResult(
                success=False, preprocessed_content=content, chunk_count=1, processing_time=time.time() - start_time,
                error_message=error_msg, original_length=original_length, preprocessed_length=original_length)

    def _ai_driven_semantic_chunking(self, content: str, project_name: str) -> str:
        """
        AI驱动的智能语义分块（滑动窗口策略）
        """
        final_content = []
        remaining_content = content
        
        while len(remaining_content) > self.ai_chunking_target_size * 1.2: # 1.2倍buffer避免产生过小尾部
            try:
                # 1. 确定理想的分割点位置
                ideal_split_pos = self.ai_chunking_target_size
                
                # 2. 围绕理想分割点，构建发送给AI的上下文窗口
                half_window = self.ai_chunking_context_window_size // 2
                start = max(0, ideal_split_pos - half_window)
                end = min(len(remaining_content), ideal_split_pos + half_window)
                context_window = remaining_content[start:end]

                logging.info(f"创建上下文窗口进行AI分析: 范围 [{start}:{end}], 窗口大小 {len(context_window)}")

                # 3. 调用AI在窗口内找到最佳分割点
                processed_window = self._call_ai_for_chunking(
                    content=context_window,
                    system_prompt=self.smart_clipping_system_prompt,
                    task_prompt_template=self.smart_clipping_task_prompt
                )

                # 4. 在处理后的窗口中找到AI插入的标记
                marker_pos_in_window = processed_window.find(self.chunk_marker)

                split_pos_in_original = -1
                if marker_pos_in_window != -1:
                    # 如果AI成功插入标记，计算该标记在`remaining_content`中的绝对位置
                    split_pos_in_original = start + marker_pos_in_window
                    logging.info(f"AI找到分割点，位于窗口内 {marker_pos_in_window}，原始文本中 {split_pos_in_original}")
                else:
                    # AI没有找到合适的分割点，采用硬分割作为后备
                    split_pos_in_original = ideal_split_pos
                    logging.warning(f"AI未在窗口内找到分割点，回退到目标位置 {ideal_split_pos} 进行硬分割")

                # 5. 分割文本并准备下一次迭代
                chunk = remaining_content[:split_pos_in_original]
                final_content.append(chunk)
                remaining_content = remaining_content[split_pos_in_original:]
                # 如果AI没有插入标记，我们手动在这里插入，以分隔我们硬分割的块
                if marker_pos_in_window == -1:
                    final_content.append(self.chunk_marker)

            except Exception as e:
                logging.error(f"AI滑动窗口分块失败: {e}", exc_info=True)
                if self.ai_chunking_fallback_to_rules:
                    logging.warning("AI分块失败，回退到规则驱动分块处理剩余内容")
                    # 对于剩余部分，使用规则分块
                    remaining_processed = self._rule_based_semantic_chunking(remaining_content, project_name)
                    final_content.append(remaining_processed)
                    remaining_content = "" # 终止循环
                    break
                else:
                    # 如果不回退，则将剩余内容直接附加并停止
                    final_content.append(remaining_content)
                    remaining_content = ""
                    logging.error("AI分块失败且未配置回退，已停止分块。")
                    break

        # 将最后剩余的部分附加到结果中
        if remaining_content:
            final_content.append(remaining_content)

        return "".join(final_content)


    def _call_ai_for_chunking(self, content: str, system_prompt: str, task_prompt_template: str) -> str:
        """
        调用AI服务执行任务
        """
        import requests
        import json

        channel = self.ai_service.select_channel()
        if not channel:
            raise Exception("没有可用的AI通道")

        task_prompt = task_prompt_template.format(content=content)

        url = channel.api_base_url.rstrip("/") + "/chat/completions"
        headers = {
            "Authorization": f"Bearer {channel.api_key}",
            "Content-Type": "application/json",
        }

        payload = {
            "model": channel.model_name,
            "messages": [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": task_prompt},
            ],
            "temperature": 0.1,
            "stream": False,
        }

        logging.debug(f"调用AI API: 通道={channel.name}, 内容长度={len(content)}")

        try:
            resp = requests.post(url, headers=headers, json=payload, timeout=self.ai_chunking_timeout)
            resp.raise_for_status() # 抛出HTTP错误
            
            response_data = resp.json()
            if 'choices' in response_data and len(response_data['choices']) > 0:
                choice = response_data['choices'][0]
                if 'message' in choice and 'content' in choice['message']:
                    result = choice['message']['content'].strip()
                    logging.debug(f"AI调用完成: 输出长度={len(result)}")
                    return result

            raise Exception(f"AI API响应格式不正确: {response_data}")

        except requests.exceptions.Timeout:
            logging.error(f"AI API调用超时: {self.ai_chunking_timeout}s")
            raise Exception("AI API调用超时")
        except requests.exceptions.RequestException as e:
            logging.error(f"AI API调用失败: {e}")
            raise Exception(f"AI API调用失败: {e}")
        except Exception as e:
            logging.error(f"AI API调用异常: {e}")
            raise e

    def _rule_based_semantic_chunking(self, content: str, project_name: str) -> str:
        """
        规则驱动的语义分块（作为备用方案）
        在固定长度附近寻找换行符进行分割。
        """
        logging.info(f"开始规则驱动语义分块: 目标块大小={self.ai_chunking_target_size}")
        
        result_parts = []
        remaining_content = content
        
        while len(remaining_content) > self.ai_chunking_target_size:
            # 找到目标位置附近最接近的换行符
            split_pos = remaining_content.rfind('\n', 0, self.ai_chunking_target_size)
            if split_pos == -1: # 如果找不到，就在目标位置硬切
                split_pos = self.ai_chunking_target_size
            
            result_parts.append(remaining_content[:split_pos])
            result_parts.append(f"\n{self.chunk_marker}\n")
            remaining_content = remaining_content[split_pos:].lstrip('\n')
        
        result_parts.append(remaining_content)
        
        return "".join(result_parts)

    def _count_chunks(self, content: str) -> int:
        """计算分块数量"""
        if not content:
            return 0
        return content.count(self.chunk_marker) + 1
    
    # 以下方法保持不变...
    def get_preprocessed_file_path(self, project_name: str, base_dir: Path) -> Path:
        """获取预处理文件路径"""
        project_dir = base_dir / "extracted" / project_name
        md_files = list(project_dir.glob("*.md"))
        if not md_files:
            raise FileNotFoundError(f"项目 {project_name} 中没有找到markdown文件")
        original_md = md_files[0]
        preprocessed_name = original_md.stem + "_preprocessed.md"
        return project_dir / preprocessed_name
    
    def save_preprocessed_content(self, content: str, file_path: Path) -> bool:
        """保存预处理内容到文件"""
        try:
            file_path.parent.mkdir(parents=True, exist_ok=True)
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            logging.info(f"预处理内容已保存到: {file_path}")
            return True
        except Exception as e:
            logging.error(f"保存预处理内容失败: {e}")
            return False
    
    def load_preprocessed_content(self, file_path: Path) -> Optional[str]:
        """加载预处理内容"""
        try:
            if not file_path.exists(): return None
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            logging.info(f"已加载预处理内容: {file_path}")
            return content
        except Exception as e:
            logging.error(f"加载预处理内容失败: {e}")
            return None
    
    def is_preprocessed_file_valid(self, file_path: Path, original_file_path: Path) -> bool:
        """检查预处理文件是否有效"""
        try:
            if not file_path.exists() or not original_file_path.exists(): return False
            preprocessed_mtime = file_path.stat().st_mtime
            original_mtime = original_file_path.stat().st_mtime
            return preprocessed_mtime > original_mtime
        except Exception as e:
            logging.error(f"检查预处理文件有效性失败: {e}")
            return False

if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    
    # 模拟AI服务
    class MockAIService:
        # 这个模拟服务将真正地实现“智能剪刀”逻辑，以便测试
        def select_channel(self):
            # 模拟返回一个有效的通道对象
            @dataclass
            class MockChannel:
                name: str = "mock_channel"
                api_base_url: str = "http://localhost:1234"
                api_key: str = "sk-mock"
                model_name: str = "mock-model"
            return MockChannel()
            
        def _call_ai_for_chunking(self, content, system_prompt, task_prompt_template):
            # 模拟AI的行为：在最接近中心的答案和问题之间插入标记
            import re
            
            # 找到所有可能的分割点
            pattern = re.compile(r'(<span style="color:rgb\(255, 0, 0\)"><strong>.*?</strong></span>\s*?)(?=\d+\.|\n\w+?图是|根据)', re.DOTALL)
            matches = list(pattern.finditer(content))
            
            if not matches:
                return content # 没找到，返回原文
            
            # 找到最接近中心的分割点
            center = len(content) // 2
            best_match = min(matches, key=lambda m: abs(m.start() - center))
            
            # 在最佳分割点后插入标记
            split_point = best_match.end()
            return content[:split_point] + "\n<!-- CHUNK_BREAK -->\n" + content[split_point:]

    # Monkey patch the actual call with our mock logic for testing
    SemanticChunker._call_ai_for_chunking = MockAIService._call_ai_for_chunking

    # 创建语义分块器
    ai_service = MockAIService()
    test_config = {
        'ENABLE_AI_CHUNKING': 'true',
        'AI_CHUNKING_TARGET_SIZE': '800',  # 设置一个较小的目标尺寸以触发分块
        'AI_CHUNKING_CONTEXT_WINDOW_SIZE': '300' # 上下文窗口
    }
    chunker = SemanticChunker(ai_service, config=test_config)
    
    # 使用你提供的真实文本作为测试内容
    test_content = """<img alt="undefined" src="images_..._page_4_img_1.png">
四种菜品中，价格涨幅最大的是(   )。
菜品A
菜品B
菜品C
<span style="color:rgb(255, 0, 0)"><strong>莱品D</strong></span>
根据2015-2019年全国生猪出栏量情况和2015-2019年国内猪肉产量占肉类总产量的比例情况图回答下列问题。
<img alt="undefined" src="images_..._page_12_img_1.png">
由于“非洲猪痘”影响，2019年全国生猪出栏量明显下降，国家为了稳定猪肉价，大力推进生猪养殖，从2020年起全国生猪出栏量每年按照10%的速度增长，问到 ()年全国生猪出栏量超过2015年水准(   )
2025
2024
2023
<span style="color:rgb(255, 0, 0)"><strong>2022</strong></span>
3.下图反映了某种产品的成本与出厂价情况(单位:元)。根据图表回答下列问题:
<img alt="undefined" src="images_..._page_20_img_1.png">
每生产一件该产品获得的利润率最高的是第(   )年
二
三
<span style="color:rgb(255, 0, 0)"><strong>四</strong></span>
五
4.下图是过去某两年，手机用户和PC/平板用户，自主安装浏览器的情况。请根据图表信息，回答问题。
<img alt="undefined" src="images_..._page_28_img_1.png">
第一年，用户自主安装的PC/平板浏览器中，前两名的市场份额之和为(   )。
42.5%
<span style="color:rgb(255, 0, 0)"><strong>43%</strong></span>
48%
66%
5.图表是我国2月份全社会客货运输量的信息。请根据图表相关信息，回答问题。
<img alt="undefined" src="images_..._page_36_img_1.png">
如果明年2月份，货运总量的增长速度是水运货运量增长速度的2.5倍，那么明年2月份的货运总量约为(   )。
42亿吨
<span style="color:rgb(255, 0, 0)"><strong>43亿吨</strong></span>
99亿吨
98亿吨
6.10月份，几个不同移动教育应用呈现出了不同的覆盖率和活跃率，具体如下。请根据图表信息，回答问题。
<img alt="undefined" src="images_..._page_44_img_1.png">
10月份不同移动教育应用中，活跃率与覆盖率之比最大的是(   )。
作业帮
<span style="color:rgb(255, 0, 0)"><strong>阿凡题</strong></span>
纳米盒
我要当学霸
"""

    # 执行语义分块
    result = chunker.preprocess_document(test_content, "real_test_project")
    
    print("-" * 50)
    print(f"分块结果: 成功={result.success}")
    print(f"原始长度: {result.original_length}")
    print(f"处理后长度: {result.preprocessed_length}")
    print(f"分块数量: {result.chunk_count}")
    print(f"处理时间: {result.processing_time:.2f}s")
    print("-" * 50)
    
    print("\n--- 处理后内容 ---\n")
    print(result.preprocessed_content)