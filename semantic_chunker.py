#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
semantic_chunker.py

语义分块器 - 实现AI驱动的智能内容分块
Stage 1: AI预处理，在源Markdown内容中插入语义分块标记
"""

import logging
import time
from pathlib import Path
from typing import Dict, Any, Optional, List
from dataclasses import dataclass


@dataclass
class ChunkingResult:
    """分块结果数据类"""
    success: bool
    preprocessed_content: str
    chunk_count: int
    processing_time: float
    error_message: str = ""
    original_length: int = 0
    preprocessed_length: int = 0


class SemanticChunker:
    """
    语义分块器
    
    使用AI分析文档内容，在语义边界处插入分块标记
    """
    
    def __init__(self, ai_service, chunk_marker: str = "<!-- CHUNK_BREAK -->"):
        """
        初始化语义分块器
        
        Args:
            ai_service: AI服务实例
            chunk_marker: 分块标记字符串
        """
        self.ai_service = ai_service
        self.chunk_marker = chunk_marker
        
        # 语义分块的系统提示词
        self.system_prompt = """你是一个专业的文档内容分析师，专门负责将长文档分割成语义完整的块。

你的任务是：
1. 分析输入的Markdown文档内容
2. 识别语义边界（完整的问答对）
3. 在适当的位置插入分块标记 `<!-- CHUNK_BREAK -->`
4. 仅仅选择一个（只需要插入一个！！！必须插入一个！！！！）合适为插入分块标记即可。这是一个大段文本的一个部分。

分块原则：
- 保持问答对的完整性，不要在问题和答案之间分割
- 保持相关概念的连贯性
- 每个块应该包含足够的上下文信息
- 保持原始格式和结构不变

输出要求：
- 直接输出处理后的Markdown内容
- 只在语义边界处添加 `<!-- CHUNK_BREAK -->` 标记
- 不要添加任何其他说明或注释
- 保持原始内容的完整性和格式"""

        self.task_prompt = """请分析以下Markdown文档内容，在适当的语义边界处插入分块标记 `<!-- CHUNK_BREAK -->`：

---

{content}

---

请直接输出处理后的内容，在语义边界处插入 `<!-- CHUNK_BREAK -->` 标记。"""

        logging.info(f"语义分块器初始化完成，分块标记: {self.chunk_marker}")
    
    def preprocess_document(self, content: str, project_name: str) -> ChunkingResult:
        """
        预处理文档，插入语义分块标记

        Args:
            content: 原始Markdown内容
            project_name: 项目名称

        Returns:
            ChunkingResult: 分块结果
        """
        start_time = time.time()
        original_length = len(content)

        logging.info(f"开始语义分块预处理: 项目={project_name}, 原始长度={original_length}字符")

        # 检查内容长度
        if original_length == 0:
            return ChunkingResult(
                success=False,
                preprocessed_content="",
                chunk_count=0,
                processing_time=0,
                error_message="输入内容为空",
                original_length=0,
                preprocessed_length=0
            )

        # 如果内容较短，不需要分块
        if original_length < 6000:  # 提高阈值，小于6000字符不分块
            logging.info(f"内容较短({original_length}字符)，跳过语义分块")
            return ChunkingResult(
                success=True,
                preprocessed_content=content,
                chunk_count=1,
                processing_time=time.time() - start_time,
                original_length=original_length,
                preprocessed_length=original_length
            )

        try:
            # 使用智能边界分析进行语义分块
            preprocessed_content = self._intelligent_semantic_chunking(content, project_name)

            # 计算分块数量
            chunk_count = self._count_chunks(preprocessed_content)

            processing_time = time.time() - start_time
            preprocessed_length = len(preprocessed_content)

            logging.info(f"语义分块完成: 生成{chunk_count}个块, 处理时间{processing_time:.2f}s")

            return ChunkingResult(
                success=True,
                preprocessed_content=preprocessed_content,
                chunk_count=chunk_count,
                processing_time=processing_time,
                original_length=original_length,
                preprocessed_length=preprocessed_length
            )

        except Exception as e:
            error_msg = f"语义分块处理异常: {str(e)}"
            logging.error(error_msg)

            return ChunkingResult(
                success=False,
                preprocessed_content=content,
                chunk_count=1,
                processing_time=time.time() - start_time,
                error_message=error_msg,
                original_length=original_length,
                preprocessed_length=original_length
            )
    
    def _intelligent_semantic_chunking(self, content: str, project_name: str) -> str:
        """
        智能语义分块 - 使用现有AI服务架构

        Args:
            content: 原始内容
            project_name: 项目名称

        Returns:
            str: 插入分块标记后的内容
        """
        lines = content.split('\n')
        result_lines = []
        current_chunk_size = 0
        target_chunk_size = 12000  # 目标块大小（12K字符）

        logging.info(f"开始智能语义分块: 目标块大小={target_chunk_size}")

        for i, line in enumerate(lines):
            result_lines.append(line)
            current_chunk_size += len(line) + 1  # +1 for newline

            # 检查是否需要考虑分块
            if current_chunk_size > target_chunk_size:
                # 使用基于规则的智能分块（结合语义理解）
                break_point = self._find_semantic_break_point(lines, i, current_chunk_size)

                if break_point is not None:
                    # 在找到的分割点插入标记
                    insert_position = break_point - (len(lines) - len(result_lines)) + 1
                    if 0 <= insert_position < len(result_lines):
                        result_lines.insert(insert_position, '')
                        result_lines.insert(insert_position + 1, self.chunk_marker)
                        result_lines.insert(insert_position + 2, '')
                        current_chunk_size = sum(len(line) + 1 for line in result_lines[insert_position + 3:])
                        logging.info(f"在第{break_point}行插入语义分块标记")
                    else:
                        # 如果位置无效，使用简单规则
                        if self._is_good_break_point(line, lines, i):
                            result_lines.extend(['', self.chunk_marker, ''])
                            current_chunk_size = 0
                            logging.info(f"在第{i}行插入简单分块标记")
                else:
                    # 如果找不到合适的分割点，使用简单规则
                    if self._is_good_break_point(line, lines, i):
                        result_lines.extend(['', self.chunk_marker, ''])
                        current_chunk_size = 0
                        logging.info(f"在第{i}行插入默认分块标记")

        return '\n'.join(result_lines)

    def _find_semantic_break_point(self, lines: List[str], current_line: int, current_chunk_size: int) -> Optional[int]:
        """
        使用基于规则的语义分析找到最佳分割点

        Args:
            lines: 所有行
            current_line: 当前行索引
            current_chunk_size: 当前块大小

        Returns:
            Optional[int]: 最佳分割点的行号，如果找不到返回None
        """
        try:
            # 向前搜索合适的分割点（最多回退200行）
            search_start = max(0, current_line - 200)
            best_break_point = None
            best_score = -1

            for i in range(current_line, search_start, -1):
                if i >= len(lines):
                    continue

                line = lines[i].strip()
                score = self._calculate_break_score(line, lines, i)

                # 如果找到高分的分割点，使用它
                if score > best_score:
                    best_score = score
                    best_break_point = i

                # 如果找到非常好的分割点，直接使用
                if score >= 8:  # 高分阈值
                    break

            # 如果找到了合适的分割点
            if best_break_point is not None and best_score >= 3:
                return best_break_point

            return None

        except Exception as e:
            logging.error(f"语义分割点查找失败: {e}")
            return None

    def _calculate_break_score(self, line: str, lines: List[str], line_index: int) -> int:
        """
        计算分割点的语义得分

        Args:
            line: 当前行内容
            lines: 所有行
            line_index: 行索引

        Returns:
            int: 分割得分（0-10，越高越适合分割）
        """
        score = 0

        # 检查是否是章节标题
        if line.startswith('#'):
            if line.startswith('# '):
                score += 10  # 一级标题，最佳分割点
            elif line.startswith('## '):
                score += 8   # 二级标题，很好的分割点
            elif line.startswith('### '):
                score += 6   # 三级标题，好的分割点
            else:
                score += 4   # 其他级别标题

        # 检查是否是问题行
        if line.startswith('问题') or line.startswith('### 问题') or '？' in line or '?' in line:
            score += 7  # 问题是很好的分割点

        # 检查是否是空行后的内容
        if line_index > 0 and not lines[line_index - 1].strip():
            score += 2  # 空行后的内容适合分割

        # 检查是否是段落开始
        if line and not line.startswith(' ') and not line.startswith('\t'):
            score += 1  # 段落开始适合分割

        # 检查是否是列表项
        if line.startswith('- ') or line.startswith('* ') or line.startswith('+ '):
            score += 3  # 列表项是好的分割点

        # 检查是否是编号列表
        if len(line) > 2 and line[0].isdigit() and line[1] == '.':
            score += 3  # 编号列表是好的分割点

        # 检查下一行是否是标题或重要内容
        if line_index + 1 < len(lines):
            next_line = lines[line_index + 1].strip()
            if next_line.startswith('#'):
                score += 5  # 下一行是标题，当前行是好的分割点

        # 避免在句子中间分割
        if line.endswith('。') or line.endswith('.') or line.endswith('！') or line.endswith('!'):
            score += 2  # 句子结尾适合分割
        elif line.endswith('，') or line.endswith(',') or line.endswith('；') or line.endswith(';'):
            score -= 2  # 句子中间不适合分割

        # 避免分割代码块
        if '```' in line or line.startswith('    ') or line.startswith('\t'):
            score -= 5  # 代码块不适合分割

        return max(0, score)  # 确保得分不为负
    
    def _is_good_break_point(self, line: str, lines: List[str], index: int) -> bool:
        """判断是否是好的分割点"""
        # 在段落结束后分割
        if line.strip() == '' and index + 1 < len(lines) and lines[index + 1].strip() != '':
            return True
        
        # 在标题前分割
        if index + 1 < len(lines) and lines[index + 1].startswith('#'):
            return True
        
        # 在问答对之间分割（简单启发式）
        if (line.strip().endswith('？') or line.strip().endswith('?') or 
            line.strip().endswith('。') or line.strip().endswith('.')):
            if index + 1 < len(lines) and lines[index + 1].strip() != '':
                return True
        
        return False
    
    def _count_chunks(self, content: str) -> int:
        """计算分块数量"""
        return content.count(self.chunk_marker) + 1
    
    def get_preprocessed_file_path(self, project_name: str, base_dir: Path) -> Path:
        """
        获取预处理文件路径
        
        Args:
            project_name: 项目名称
            base_dir: 基础目录
            
        Returns:
            Path: 预处理文件路径
        """
        project_dir = base_dir / "extracted" / project_name
        
        # 查找原始markdown文件
        md_files = list(project_dir.glob("*.md"))
        if not md_files:
            raise FileNotFoundError(f"项目 {project_name} 中没有找到markdown文件")
        
        # 使用第一个markdown文件作为基础
        original_md = md_files[0]
        
        # 生成预处理文件名
        preprocessed_name = original_md.stem + "_preprocessed.md"
        return project_dir / preprocessed_name
    
    def save_preprocessed_content(self, content: str, file_path: Path) -> bool:
        """
        保存预处理内容到文件
        
        Args:
            content: 预处理后的内容
            file_path: 保存路径
            
        Returns:
            bool: 是否保存成功
        """
        try:
            # 确保目录存在
            file_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 保存内容
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            logging.info(f"预处理内容已保存到: {file_path}")
            return True
            
        except Exception as e:
            logging.error(f"保存预处理内容失败: {e}")
            return False
    
    def load_preprocessed_content(self, file_path: Path) -> Optional[str]:
        """
        加载预处理内容
        
        Args:
            file_path: 文件路径
            
        Returns:
            Optional[str]: 预处理内容，如果失败返回None
        """
        try:
            if not file_path.exists():
                return None
            
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            logging.info(f"已加载预处理内容: {file_path}")
            return content
            
        except Exception as e:
            logging.error(f"加载预处理内容失败: {e}")
            return None
    
    def is_preprocessed_file_valid(self, file_path: Path, original_file_path: Path) -> bool:
        """
        检查预处理文件是否有效（比原始文件新）
        
        Args:
            file_path: 预处理文件路径
            original_file_path: 原始文件路径
            
        Returns:
            bool: 是否有效
        """
        try:
            if not file_path.exists() or not original_file_path.exists():
                return False
            
            preprocessed_mtime = file_path.stat().st_mtime
            original_mtime = original_file_path.stat().st_mtime
            
            return preprocessed_mtime > original_mtime
            
        except Exception as e:
            logging.error(f"检查预处理文件有效性失败: {e}")
            return False


if __name__ == "__main__":
    # 测试代码
    logging.basicConfig(level=logging.INFO)
    
    # 模拟AI服务
    class MockAIService:
        def __init__(self):
            self.config = {}
        
        def call_api(self, chunk, idx, cache_dir):
            return True
    
    # 创建语义分块器
    ai_service = MockAIService()
    chunker = SemanticChunker(ai_service)
    
    # 测试内容
    test_content = """# 测试文档

这是第一个段落，包含一些基本信息。

## 问题1
这是第一个问题的描述？

这是第一个问题的答案。答案包含详细的解释和说明。

## 问题2
这是第二个问题的描述？

这是第二个问题的答案。答案也包含详细的解释。

## 总结
这是文档的总结部分。
""" * 10  # 重复内容使其足够长
    
    # 执行语义分块
    result = chunker.preprocess_document(test_content, "test_project")
    
    print(f"分块结果: 成功={result.success}")
    print(f"原始长度: {result.original_length}")
    print(f"处理后长度: {result.preprocessed_length}")
    print(f"分块数量: {result.chunk_count}")
    print(f"处理时间: {result.processing_time:.2f}s")
    
    if result.chunk_count > 1:
        print("\n分块标记位置:")
        lines = result.preprocessed_content.split('\n')
        for i, line in enumerate(lines):
            if chunker.chunk_marker in line:
                print(f"  第 {i+1} 行: {line}")
