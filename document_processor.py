#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
document_processor.py

统一的文档处理器，支持多种文档格式的提取和转换
整合PDF、Word等文档类型的处理，提供统一的接口
"""

import sys
import logging
from pathlib import Path
from typing import Dict, List, Optional, Union, Tuple

from file_manager import FileManager
from pdf_extractor import PDFExtractor
from word_extractor import WordExtractor
from ai_service import AIService
from multi_channel_config import MultiChannelConfigParser
from anki_generator import AnkiGenerator
from md_to_anki import make_chunks, read_input_lines
from semantic_chunker import SemanticChunker
from chunk_processor import ChunkProcessor


class DocumentProcessor:
    """统一文档处理器"""
    
    def __init__(self, base_dir: str = "documents", config_file: str = "config.ini", prompt_template: str = None):
        """
        初始化文档处理器

        Args:
            base_dir: 基础工作目录
            config_file: 配置文件路径
            prompt_template: 指定使用的提示词模板名称
        """
        self.file_manager = FileManager(base_dir)

        # 使用新的多通道配置解析器
        self.config_parser = MultiChannelConfigParser(config_file)
        self.config = self.config_parser.get_config()

        # 初始化各种提取器
        self.pdf_extractor = PDFExtractor(self.file_manager)
        try:
            self.word_extractor = WordExtractor(self.file_manager)
            self.word_available = True
        except ImportError:
            self.word_extractor = None
            self.word_available = False
            logging.warning("Word提取器不可用，请安装 python-docx")

        # 存储指定的提示词模板
        self.prompt_template = prompt_template
        
        # 支持的文档类型
        self.supported_extensions = {
            '.pdf': 'pdf',
            '.docx': 'word',
            '.doc': 'word',
            '.md': 'markdown',
        }

        # 初始化AI服务和Anki生成器（如果配置了AI相关参数）
        self.ai_service = None
        self.anki_generator = None
        if self._has_ai_config():
            try:
                # 使用重构后的AI服务（原生支持多通道）
                self.ai_service = AIService(self.config)

                # 如果指定了提示词模板，设置使用该模板
                if self.prompt_template:
                    success = self.ai_service.set_prompt_template(self.prompt_template)
                    if success:
                        logging.info(f"已设置提示词模板: {self.prompt_template}")
                    else:
                        logging.warning(f"设置提示词模板失败: {self.prompt_template}，将使用默认模板")

                self.anki_generator = AnkiGenerator()

                # 初始化语义分块器和分块处理器
                chunk_marker = self.config.get('CHUNK_MARKER', '<!-- CHUNK_BREAK -->')
                self.semantic_chunker = SemanticChunker(self.ai_service, chunk_marker)
                self.chunk_processor = ChunkProcessor(self.ai_service, chunk_marker)

                logging.info("AI服务、Anki生成器和语义分块器初始化成功")

                # 显示通道信息
                stats = self.ai_service.get_stats()
                logging.info(f"已启用 {stats['active_channels']} 个AI通道")

            except Exception as e:
                logging.error(f"AI服务初始化失败: {e}")
                self.ai_service = None
                self.anki_generator = None
    


    def _has_ai_config(self) -> bool:
        """检查是否配置了AI相关参数"""
        return self.config_parser.has_ai_channels()

    def _copy_images_for_anki(self, project_name: str) -> bool:
        """
        将项目的图片从extracted文件夹复制到images文件夹，并按项目名称组织

        Args:
            project_name: 项目名称

        Returns:
            bool: 是否成功复制图片
        """
        import shutil

        # 修复：正确获取项目路径 - 项目直接在 extracted 目录下
        extracted_dir = self.file_manager.base_dir / "extracted" / project_name

        if not extracted_dir.exists():
            logging.info(f"项目 {project_name} 没有提取的文件夹，跳过图片复制")
            return True

        # 查找项目中的图片文件夹（可能是 images 或重命名后的文件夹）
        image_folders = []
        for item in extracted_dir.iterdir():
            if item.is_dir() and ('images' in item.name.lower()):
                image_folders.append(item)

        if not image_folders:
            logging.info(f"项目 {project_name} 没有图片文件夹，跳过图片复制")
            return True

        # 创建images目录结构
        images_base_dir = Path("images")
        images_project_dir = images_base_dir / project_name

        try:
            images_project_dir.mkdir(parents=True, exist_ok=True)

            # 复制所有图片文件
            image_extensions = {'.png', '.jpg', '.jpeg', '.gif', '.bmp', '.svg'}
            copied_count = 0

            for image_folder in image_folders:
                for img_file in image_folder.iterdir():
                    if img_file.is_file() and img_file.suffix.lower() in image_extensions:
                        dest_file = images_project_dir / img_file.name
                        shutil.copy2(img_file, dest_file)
                        copied_count += 1

            logging.info(f"成功复制 {copied_count} 个图片文件到 {images_project_dir}")
            return True

        except Exception as e:
            logging.error(f"复制图片文件失败: {e}")
            return False

    def _update_image_paths_in_markdown(self, markdown_content: str, project_name: str) -> str:
        """
        更新Markdown内容中的图片路径，从extracted路径改为Anki格式路径

        Args:
            markdown_content: 原始Markdown内容
            project_name: 项目名称

        Returns:
            str: 更新后的Markdown内容
        """
        # 使用新的工具模块
        from document_utils import MarkdownImagePathUpdater

        path_updater = MarkdownImagePathUpdater()
        return path_updater.update_image_paths_for_anki(markdown_content, project_name, "images")
    
    def process_document(self, file_path: str) -> Dict[str, Union[str, int]]:
        """
        处理单个文档
        
        Args:
            file_path: 文档文件路径
            
        Returns:
            处理结果字典
        """
        file_path = Path(file_path)
        
        if not file_path.exists():
            raise FileNotFoundError(f"文档文件不存在: {file_path}")
        
        # 检查文件类型
        file_ext = file_path.suffix.lower()
        if file_ext not in self.supported_extensions:
            raise ValueError(f"不支持的文档类型: {file_ext}")
        
        doc_type = self.supported_extensions[file_ext]
        
        logging.info(f"开始处理 {doc_type.upper()} 文档: {file_path}")
        
        # 根据文档类型调用相应的提取器
        if doc_type == 'pdf':
            result = self.pdf_extractor.extract_from_pdf(
                str(file_path),
                self.config['pdf_top_margin'],
                self.config['pdf_bottom_margin']
            )
        elif doc_type == 'word':
            if not self.word_available:
                raise RuntimeError("Word提取器不可用，请安装 python-docx")
            result = self.word_extractor.extract_from_word(str(file_path))
        else:
            raise ValueError(f"未实现的文档类型处理器: {doc_type}")
        
        # 添加通用信息
        result['document_type'] = doc_type
        result['original_file'] = str(file_path)
        result['file_size'] = file_path.stat().st_size
        
        logging.info(f"{doc_type.upper()} 文档处理完成: {result['project_name']}")
        
        return result
    
    def process_multiple_documents(self, file_paths: List[str]) -> List[Dict[str, Union[str, int]]]:
        """
        批量处理多个文档
        
        Args:
            file_paths: 文档文件路径列表
            
        Returns:
            处理结果列表
        """
        results = []
        
        logging.info(f"开始批量处理 {len(file_paths)} 个文档")
        
        for i, file_path in enumerate(file_paths, 1):
            try:
                logging.info(f"[{i}/{len(file_paths)}] 处理文档: {file_path}")
                result = self.process_document(file_path)
                results.append(result)
                
            except Exception as e:
                logging.error(f"处理文档 {file_path} 失败: {e}")
                # 添加失败记录
                results.append({
                    'original_file': file_path,
                    'error': str(e),
                    'success': False
                })
        
        # 统计处理结果
        success_count = sum(1 for r in results if r.get('success', True) and 'error' not in r)
        failed_count = len(results) - success_count
        
        logging.info(f"批量处理完成: 成功 {success_count}, 失败 {failed_count}")
        
        return results
    
    def process_directory(self, directory: str, recursive: bool = True) -> List[Dict[str, Union[str, int]]]:
        """
        处理目录中的所有支持的文档
        
        Args:
            directory: 目录路径
            recursive: 是否递归处理子目录
            
        Returns:
            处理结果列表
        """
        dir_path = Path(directory)
        
        if not dir_path.exists() or not dir_path.is_dir():
            raise ValueError(f"目录不存在或不是有效目录: {directory}")
        
        # 查找支持的文档文件
        file_paths = []
        
        if recursive:
            for ext in self.supported_extensions.keys():
                file_paths.extend(dir_path.rglob(f"*{ext}"))
        else:
            for ext in self.supported_extensions.keys():
                file_paths.extend(dir_path.glob(f"*{ext}"))
        
        # 转换为字符串路径
        file_paths = [str(fp) for fp in file_paths]
        
        logging.info(f"在目录 {directory} 中找到 {len(file_paths)} 个支持的文档")
        
        if not file_paths:
            logging.warning("未找到任何支持的文档文件")
            return []
        
        return self.process_multiple_documents(file_paths)
    
    def get_processing_summary(self) -> Dict[str, Union[int, List]]:
        """
        获取处理摘要信息
        
        Returns:
            处理摘要字典
        """
        projects = self.file_manager.list_projects()
        
        summary = {
            'total_projects': len(projects),
            'projects_with_images': sum(1 for p in projects if p['has_images']),
            'projects_with_cache': sum(1 for p in projects if p['cache_progress']['cached'] > 0),
            'projects_with_anki': sum(1 for p in projects if p['has_anki']),
            'total_images': sum(p['image_count'] for p in projects),
            'projects': projects
        }
        
        return summary
    
    def generate_anki_for_project(self, project_name: str, interactive: bool = True) -> Dict[str, Union[str, int]]:
        """
        为指定项目生成Anki闪卡，支持模糊匹配

        Args:
            project_name: 项目名称（支持部分匹配）
            interactive: 是否启用交互式选择

        Returns:
            生成结果字典
        """
        if not self.ai_service or not self.anki_generator:
            raise RuntimeError("AI服务未初始化，请检查配置文件中的API相关设置")

        # 使用智能项目匹配
        from project_matcher import ProjectMatcher
        from markdown_detector import MarkdownDetector

        matcher = ProjectMatcher(str(self.file_manager.base_dir))
        md_detector = MarkdownDetector()

        # 查找匹配的项目
        if interactive:
            match_result = matcher.interactive_project_selection(project_name)
            if not match_result:
                raise ValueError(f"未找到匹配的项目: {project_name}")

            actual_project_name, markdown_file = match_result
        else:
            # 非交互模式，使用最佳匹配
            best_match = matcher.get_best_match(project_name)
            if not best_match:
                raise ValueError(f"未找到匹配的项目: {project_name}")

            actual_project_name = best_match['name']
            markdown_file = md_detector.select_best_markdown(best_match['path'], actual_project_name)

            if not markdown_file:
                raise FileNotFoundError(f"项目 {actual_project_name} 中没有找到markdown文件")

        # 验证markdown文件
        is_valid, error_msg = md_detector.validate_markdown_file(markdown_file)
        if not is_valid:
            raise FileNotFoundError(f"Markdown文件无效: {error_msg}")

        logging.info(f"开始为项目 {actual_project_name} 生成Anki闪卡")
        logging.info(f"使用markdown文件: {markdown_file}")

        # 注意：不再复制图片到images文件夹，图片已在项目文件夹内组织良好
        logging.info("跳过图片复制步骤 - 图片已在项目文件夹内正确组织")

        # 读取并处理Markdown内容
        try:
            with open(markdown_file, 'r', encoding='utf-8') as f:
                markdown_content = f.read()

            # 图片路径已经在项目文件夹内正确组织，无需更新

            # 获取项目配置
            project_config = self.file_manager.get_config_for_project(actual_project_name)
            project_config['INPUT_FILE'] = markdown_file  # 使用实际找到的markdown文件

            # 检查是否启用智能分块
            enable_smart_chunking = self.config.get('ENABLE_SMART_CHUNKING', 'false').lower() == 'true'

            if enable_smart_chunking and self.semantic_chunker and self.chunk_processor:
                # 使用两阶段智能分块处理
                result = self._process_with_semantic_chunking(
                    markdown_content,
                    actual_project_name,
                    project_config
                )
            else:
                # 使用传统字符分块处理
                result = self._process_markdown_to_anki(
                    markdown_content,
                    actual_project_name,
                    project_config
                )

            return result

        except Exception as e:
            logging.error(f"生成Anki闪卡失败: {e}")
            raise

    def _process_markdown_to_anki(self, markdown_content: str, project_name: str, project_config: Dict) -> Dict[str, Union[str, int]]:
        """
        处理Markdown内容生成Anki闪卡

        Args:
            markdown_content: Markdown内容
            project_name: 项目名称
            project_config: 项目配置

        Returns:
            处理结果字典
        """
        from concurrent.futures import ThreadPoolExecutor, as_completed
        from tqdm import tqdm

        # 将内容写入临时文件，然后使用现有的读取函数
        import tempfile
        with tempfile.NamedTemporaryFile(mode='w', suffix='.md', delete=False, encoding='utf-8') as temp_file:
            temp_file.write(markdown_content)
            temp_file_path = temp_file.name

        try:
            # 使用现有的读取函数
            lines = read_input_lines(temp_file_path, self.config.get('lines_to_skip', 13))
        finally:
            # 清理临时文件
            Path(temp_file_path).unlink(missing_ok=True)

        if not lines:
            raise ValueError("处理后的Markdown内容为空")

        logging.info(f"准备处理 {len(lines)} 行Markdown内容")

        # 创建文本块
        chunks = make_chunks(lines, self.config['chunk_size'], self.config['chunk_stride'])

        if not chunks:
            raise ValueError("没有生成任何文本块")

        # 设置缓存目录
        cache_dir = Path(project_config['CACHE_DIR'])
        cache_dir.mkdir(exist_ok=True)

        # 并发处理文本块
        logging.info(f"开始处理 {len(chunks)} 个文本块...")

        # 设置项目名称到AI服务配置中，用于失败块日志记录
        if hasattr(self.ai_service, 'config'):
            self.ai_service.config['project_name'] = project_name

        # 计算总的并发数：所有通道的MAX_CONCURRENT之和
        total_max_concurrent = 0
        if hasattr(self.ai_service, 'channels'):
            for channel in self.ai_service.channels:
                if channel.enabled:
                    total_max_concurrent += channel.max_concurrent
        else:
            # 单通道模式，使用配置的max_workers
            total_max_concurrent = self.config.get('max_workers', 10)

        # 实际并发数不超过块数
        max_workers = min(total_max_concurrent, len(chunks))
        logging.info(f"使用 {max_workers} 个并发工作进程（总通道并发能力: {total_max_concurrent}）")

        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交需要处理的任务（跳过已缓存的）
            futures = {}
            for idx, text in chunks:
                cache_file = cache_dir / f"{idx}.json"
                if not cache_file.exists():
                    future = executor.submit(self.ai_service.call_api, text, idx, str(cache_dir))
                    futures[future] = idx

            # 显示进度并等待结果
            success_count = 0
            if futures:
                logging.info(f"需要处理 {len(futures)} 个新块，{len(chunks) - len(futures)} 个块已缓存")

                for fut in tqdm(as_completed(futures), total=len(futures), desc="AI 处理进度"):
                    idx = futures[fut]
                    try:
                        success = fut.result()
                        if success:
                            success_count += 1
                    except Exception as e:
                        logging.error(f"块 {idx} 处理异常: {e}")

                logging.info(f"AI 处理完成: {success_count}/{len(futures)} 个块成功处理")
            else:
                logging.info("所有块都已缓存，跳过 AI 处理")

        # 生成Anki文件
        output_file = project_config['OUTPUT_FILE']
        self.anki_generator.cache_dir = cache_dir
        stats = self.anki_generator.generate_anki_file(output_file, len(chunks))

        if stats["total_cards"] == 0:
            raise ValueError("没有提取到任何问答对，请检查输入内容格式")

        logging.info(f"成功生成 {stats['total_cards']} 张Anki卡片")

        return {
            'project_name': project_name,
            'input_file': project_config['INPUT_FILE'],
            'output_file': output_file,
            'cache_dir': str(cache_dir),
            'total_cards': stats['total_cards'],
            'questions_with_images': stats['questions_with_images'],
            'total_images': stats['total_images'],
            'source_chunks': stats['source_chunks'],
            'status': 'completed'
        }

    def _process_with_semantic_chunking(self, markdown_content: str, project_name: str,
                                      project_config: Dict) -> Dict[str, Union[str, int]]:
        """
        使用两阶段语义分块处理Markdown内容生成Anki闪卡

        Stage 1: AI预处理插入语义分块标记
        Stage 2: 处理语义分块生成Anki卡片

        Args:
            markdown_content: Markdown内容
            project_name: 项目名称
            project_config: 项目配置

        Returns:
            处理结果字典
        """
        logging.info(f"开始两阶段语义分块处理: 项目={project_name}")

        # 获取预处理文件路径
        base_dir = Path(self.config['BASE_DIR'])
        preprocessed_file_path = self.semantic_chunker.get_preprocessed_file_path(project_name, base_dir)
        original_file_path = Path(project_config['INPUT_FILE'])

        # 检查是否需要重新预处理
        need_preprocessing = True
        if preprocessed_file_path.exists():
            if self.semantic_chunker.is_preprocessed_file_valid(preprocessed_file_path, original_file_path):
                logging.info("发现有效的预处理文件，跳过Stage 1预处理")
                preprocessed_content = self.semantic_chunker.load_preprocessed_content(preprocessed_file_path)
                if preprocessed_content:
                    need_preprocessing = False
                    logging.info(f"已加载预处理内容: {len(preprocessed_content)}字符")

        if need_preprocessing:
            # Stage 1: AI预处理插入语义分块标记
            logging.info("🔄 Stage 1: 开始AI语义分块预处理...")
            chunking_result = self.semantic_chunker.preprocess_document(markdown_content, project_name)

            if not chunking_result.success:
                logging.warning(f"语义分块失败，回退到传统分块: {chunking_result.error_message}")
                return self._process_markdown_to_anki(markdown_content, project_name, project_config)

            preprocessed_content = chunking_result.preprocessed_content

            # 保存预处理结果
            if self.semantic_chunker.save_preprocessed_content(preprocessed_content, preprocessed_file_path):
                logging.info(f"✅ Stage 1完成: 生成{chunking_result.chunk_count}个语义块，"
                           f"处理时间{chunking_result.processing_time:.2f}s")
            else:
                logging.warning("预处理内容保存失败，但继续处理")

        # Stage 2: 处理语义分块生成Anki卡片
        logging.info("🔄 Stage 2: 开始处理语义分块生成Anki...")

        # 获取分块统计信息
        chunk_stats = self.chunk_processor.get_chunk_statistics(preprocessed_content)
        logging.info(f"语义分块统计: {chunk_stats['effective_chunks']}个有效块")

        # 验证分块质量
        validation = chunk_stats['validation']
        if not validation['valid']:
            logging.warning(f"语义分块质量警告: {validation['reason']}")

        # 处理语义分块
        processing_result = self.chunk_processor.process_semantic_chunks(
            preprocessed_content,
            project_name,
            project_config
        )

        if not processing_result.success:
            logging.error(f"语义分块处理失败: {processing_result.error_message}")
            # 回退到传统分块
            logging.info("回退到传统字符分块处理")
            return self._process_markdown_to_anki(markdown_content, project_name, project_config)

        # 生成Anki文件
        output_file = project_config['OUTPUT_FILE']
        cache_dir = Path(processing_result.cache_dir)

        self.anki_generator.cache_dir = cache_dir
        stats = self.anki_generator.generate_anki_file(output_file, processing_result.total_chunks)

        if stats["total_cards"] == 0:
            logging.warning("语义分块未生成任何卡片，回退到传统分块")
            return self._process_markdown_to_anki(markdown_content, project_name, project_config)

        logging.info(f"✅ Stage 2完成: 成功生成{stats['total_cards']}张Anki卡片，"
                   f"处理时间{processing_result.processing_time:.2f}s")

        return {
            'project_name': project_name,
            'input_file': project_config['INPUT_FILE'],
            'output_file': output_file,
            'cache_dir': str(cache_dir),
            'total_cards': stats['total_cards'],
            'questions_with_images': stats['questions_with_images'],
            'total_images': stats['total_images'],
            'source_chunks': stats['source_chunks'],
            'status': 'completed',
            'processing_mode': 'semantic_chunking',
            'semantic_chunks': processing_result.total_chunks,
            'preprocessed_file': str(preprocessed_file_path)
        }



    def process_markdown_to_anki(self, markdown_file: str, output_file: str = None,
                                cache_dir: str = None, project_name: str = None) -> Dict[str, Union[str, int]]:
        """
        直接处理Markdown文件生成Anki闪卡

        Args:
            markdown_file: Markdown文件路径
            output_file: 输出Anki文件路径，默认为 markdown文件名.txt
            cache_dir: 缓存目录，默认为 cache
            project_name: 项目名称（用于图片管理）

        Returns:
            处理结果字典
        """
        if not self.ai_service or not self.anki_generator:
            raise RuntimeError("AI服务未初始化，请检查配置文件中的API相关设置")

        markdown_path = Path(markdown_file)
        if not markdown_path.exists():
            raise FileNotFoundError(f"Markdown文件不存在: {markdown_file}")

        # 设置默认参数
        if output_file is None:
            output_file = markdown_path.with_suffix('.txt').name

        if cache_dir is None:
            cache_dir = "cache"

        if project_name is None:
            project_name = markdown_path.stem

        logging.info(f"开始处理Markdown文件: {markdown_file}")

        # 注意：不再复制图片，图片已在项目文件夹内正确组织
        logging.info("跳过图片复制步骤 - 图片已在项目文件夹内正确组织")

        # 读取并处理Markdown内容
        try:
            with open(markdown_file, 'r', encoding='utf-8') as f:
                markdown_content = f.read()

            # 图片路径已经在项目文件夹内正确组织，无需更新

            # 创建临时项目配置
            temp_config = {
                'INPUT_FILE': str(markdown_path),
                'OUTPUT_FILE': output_file,
                'CACHE_DIR': cache_dir
            }

            # 检查是否启用智能分块
            enable_smart_chunking = self.config.get('ENABLE_SMART_CHUNKING', 'false').lower() == 'true'

            if enable_smart_chunking and self.semantic_chunker and self.chunk_processor:
                # 使用两阶段智能分块处理
                result = self._process_with_semantic_chunking(
                    markdown_content,
                    project_name,
                    temp_config
                )
            else:
                # 使用传统字符分块处理
                result = self._process_markdown_to_anki(
                    markdown_content,
                    project_name,
                    temp_config
                )

            return result

        except Exception as e:
            logging.error(f"处理Markdown文件失败: {e}")
            raise


def main():
    """命令行接口"""
    if len(sys.argv) < 2:
        print("使用方法:")
        print("  python document_processor.py <file_path>                    # 处理单个文档为Markdown")
        print("  python document_processor.py --dir <directory>              # 处理目录中的所有文档为Markdown")
        print("  python document_processor.py --list                         # 列出所有项目")
        print("  python document_processor.py --summary                      # 显示处理摘要")
        print("  python document_processor.py --anki <partial_name>          # 智能匹配项目生成Anki闪卡")
        print("  python document_processor.py --anki <partial_name> --no-interactive  # 非交互模式")
        print("  python document_processor.py --md-to-anki <md_file>         # 直接将任意Markdown文件转换为Anki")
        print("  python document_processor.py --full <file_path>             # 完整流程：文档→MD→Anki")
        print("  python document_processor.py --channels                     # 显示AI通道状态")
        print("  python document_processor.py --test-channels               # 测试AI通道连接")
        print("  python document_processor.py --prompts                     # 管理提示词模板")
        print("  python document_processor.py --list-prompts                # 列出所有可用的提示词模板")
        print("")
        print("失败块管理:")
        print("  python document_processor.py --failed-stats                # 显示失败块统计信息")
        print("  python document_processor.py --failed-list                 # 列出失败的块")
        print("  python document_processor.py --failed-retry                # 重试失败的块")
        print("  python document_processor.py --failed-clear                # 清理失败块日志")
        print("")
        print("智能分块管理:")
        print("  python document_processor.py --chunking-stats <project>    # 显示项目的分块统计信息")
        print("  python document_processor.py --chunking-preview <project>  # 预览项目的语义分块")
        print("  python document_processor.py --chunking-enable             # 启用智能分块模式")
        print("  python document_processor.py --chunking-disable            # 禁用智能分块模式")
        print("")
        print("提示词模板选择:")
        print("  --prompt <template_name>                                    # 指定使用的提示词模板")
        print("  示例: --anki project_name --prompt multiple_choice         # 使用选择题模板生成Anki")
        print("  示例: --full document.pdf --prompt default_reference_answer # 使用参考答案模板处理文档")
        print("")
        print("--anki 命令支持模糊匹配:")
        print("  示例: --anki tiku_2  可以匹配 tiku_20250615_003402")
        print("  最少需要5个字符，支持前缀匹配和包含匹配")
        sys.exit(1)
    
    # 设置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s [%(levelname)s] %(message)s'
    )

    # 解析 --prompt 参数
    prompt_template = None
    if '--prompt' in sys.argv:
        try:
            prompt_index = sys.argv.index('--prompt')
            if prompt_index + 1 < len(sys.argv):
                prompt_template = sys.argv[prompt_index + 1]
                # 从参数列表中移除 --prompt 和模板名称，避免影响其他参数解析
                sys.argv.pop(prompt_index)  # 移除 --prompt
                sys.argv.pop(prompt_index)  # 移除模板名称（索引会自动调整）
                logging.info(f"指定使用提示词模板: {prompt_template}")
            else:
                print("❌ --prompt 参数需要指定模板名称")
                sys.exit(1)
        except ValueError:
            pass  # --prompt 不在参数列表中

    processor = DocumentProcessor(prompt_template=prompt_template)
    
    try:
        if sys.argv[1] == '--dir':
            # 处理目录
            if len(sys.argv) < 3:
                print("请指定目录路径")
                sys.exit(1)
            
            directory = sys.argv[2]
            results = processor.process_directory(directory)
            
            print(f"\n目录处理完成，共处理 {len(results)} 个文档:")
            for result in results:
                if 'error' in result:
                    print(f"  ❌ {result['original_file']}: {result['error']}")
                else:
                    print(f"  ✅ {result['project_name']}: {result['document_type'].upper()}")
        
        elif sys.argv[1] == '--list':
            # 列出项目
            projects = processor.file_manager.list_projects()
            print(f"\n共有 {len(projects)} 个项目:")
            for project in projects:
                status = []
                if project['has_images']:
                    status.append(f"{project['image_count']}张图片")

                # 使用新的缓存进度信息
                cache_info = project['cache_progress']
                if cache_info['cached'] > 0:
                    status.append(f"缓存{cache_info['status']}")

                if project['has_anki']:
                    status.append("已生成Anki")

                status_str = f" ({', '.join(status)})" if status else ""
                print(f"  - {project['name']}{status_str}")
        
        elif sys.argv[1] == '--summary':
            # 显示摘要
            summary = processor.get_processing_summary()
            print("\n处理摘要:")
            print(f"  总项目数: {summary['total_projects']}")
            print(f"  包含图片的项目: {summary['projects_with_images']}")
            print(f"  已缓存的项目: {summary['projects_with_cache']}")
            print(f"  已生成Anki的项目: {summary['projects_with_anki']}")
            print(f"  总图片数: {summary['total_images']}")

        elif sys.argv[1] == '--channels':
            # 显示AI通道状态
            if processor.ai_service and hasattr(processor.ai_service, 'get_stats'):
                stats = processor.ai_service.get_stats()
                print("\nAI通道状态:")
                print(f"  总通道数: {stats['total_channels']}")
                print(f"  活跃通道数: {stats['active_channels']}")
                print("\n通道详情:")

                for name, channel_stats in stats['channels'].items():
                    status_icon = "✅" if channel_stats['status'] == 'active' else "❌"
                    print(f"  {status_icon} {name}:")
                    print(f"    状态: {channel_stats['status']}")
                    print(f"    总请求数: {channel_stats['total_requests']}")
                    print(f"    成功率: {channel_stats['success_rate']}%")
                    print(f"    平均响应时间: {channel_stats['avg_response_time']}s")
                    if channel_stats['error_message']:
                        print(f"    错误信息: {channel_stats['error_message']}")
                    print()
            else:
                print("\n❌ AI服务未初始化或不支持多通道功能")



        elif sys.argv[1] == '--test-channels':
            # 测试AI通道连接
            if processor.ai_service and hasattr(processor.ai_service, 'test_all_channels'):
                print("\n🔧 开始测试AI通道连接...")

                try:
                    test_results = processor.ai_service.test_all_channels()

                    print("\nAI通道测试结果:")
                    print("=" * 50)

                    success_count = 0
                    total_count = len(test_results)

                    for channel_name, result in test_results.items():
                        if result['success']:
                            print(f"✅ {channel_name}: 测试成功 ({result['response_time']}s)")
                            success_count += 1
                        else:
                            print(f"❌ {channel_name}: {result['error']} ({result['response_time']}s)")

                    print(f"\n测试完成: {success_count}/{total_count} 个通道可用")

                    if success_count == 0:
                        print("⚠️  警告: 没有可用的AI通道，请检查配置文件")
                    elif success_count < total_count:
                        print("⚠️  部分通道不可用，建议检查配置")
                    else:
                        print("🎉 所有通道测试通过!")

                except Exception as e:
                    print(f"❌ 通道测试失败: {e}")
            else:
                print("\n❌ AI服务未初始化或不支持通道测试功能")

        elif sys.argv[1] == '--list-prompts':
            # 列出所有可用的提示词模板
            if processor.ai_service and hasattr(processor.ai_service, 'prompt_manager'):
                print("\n📝 可用的提示词模板")
                print("=" * 50)

                # 显示当前模板
                current_template = processor.ai_service.get_current_template_info()
                if current_template:
                    print(f"当前默认模板: {current_template['name']} - {current_template['description']}")
                    print(f"题目类型: {current_template['question_type']}")
                    print(f"答案格式: {current_template['answer_format']}")
                    print()

                print("所有可用模板:")
                templates = processor.ai_service.get_prompt_templates()
                for template in templates:
                    current_mark = " (当前默认)" if template['is_current'] else ""
                    print(f"  📋 {template['name']}: {template['description']}{current_mark}")
                    print(f"     类型: {template['question_type']}, 格式: {template['answer_format']}")
                    print()

                print("使用方法:")
                print("  --prompt <template_name>  指定使用特定模板")
                print("  示例: --anki project_name --prompt multiple_choice")
            else:
                print("\n❌ AI服务未初始化或不支持提示词管理功能")

        elif sys.argv[1] == '--prompts':
            # 提示词模板管理
            if processor.ai_service and hasattr(processor.ai_service, 'prompt_manager'):
                print("\n📝 提示词模板管理")
                print("=" * 50)

                # 显示当前模板
                current_template = processor.ai_service.get_current_template_info()
                if current_template:
                    print(f"当前模板: {current_template['name']} - {current_template['description']}")
                    print(f"题目类型: {current_template['question_type']}")
                    print(f"答案格式: {current_template['answer_format']}")
                else:
                    print("当前没有设置模板")

                print("\n可用模板:")
                templates = processor.ai_service.get_prompt_templates()
                for i, template in enumerate(templates, 1):
                    current_mark = " (当前)" if template['is_current'] else ""
                    print(f"  {i}. {template['name']}: {template['description']}{current_mark}")
                    print(f"     类型: {template['question_type']}, 格式: {template['answer_format']}")

                # 交互式选择
                if len(templates) > 1:
                    try:
                        choice = input(f"\n请选择模板 (1-{len(templates)}, 回车跳过): ").strip()
                        if choice and choice.isdigit():
                            choice_idx = int(choice) - 1
                            if 0 <= choice_idx < len(templates):
                                template_name = templates[choice_idx]['name']
                                success = processor.ai_service.set_prompt_template(template_name)
                                if success:
                                    print(f"✅ 已切换到模板: {template_name}")
                                else:
                                    print("❌ 切换模板失败")
                            else:
                                print("❌ 无效的选择")
                    except KeyboardInterrupt:
                        print("\n操作已取消")
                    except Exception as e:
                        print(f"❌ 操作失败: {e}")
            else:
                print("\n❌ AI服务未初始化或不支持提示词管理功能")

        elif sys.argv[1] == '--anki':
            # 为项目生成Anki闪卡（支持模糊匹配）
            if len(sys.argv) < 3:
                print("请指定项目名称（支持部分匹配，至少5个字符）")
                print("示例: python document_processor.py --anki tiku_2")
                sys.exit(1)

            project_name = sys.argv[2]

            # 检查是否有--no-interactive参数
            interactive = '--no-interactive' not in sys.argv

            try:
                print(f"🔍 搜索匹配项目: '{project_name}'...")
                result = processor.generate_anki_for_project(project_name, interactive=interactive)

                # 使用用户反馈系统显示成功信息
                from user_feedback import UserFeedback
                feedback = UserFeedback(processor.file_manager)
                feedback.show_success_summary(result)

            except ValueError as e:
                from user_feedback import UserFeedback
                feedback = UserFeedback(processor.file_manager)

                if "未找到匹配的项目" in str(e):
                    feedback.show_project_not_found_help(project_name)
                else:
                    print(f"❌ 项目匹配失败: {e}")
                sys.exit(1)

            except FileNotFoundError as e:
                from user_feedback import UserFeedback
                feedback = UserFeedback(processor.file_manager)

                if "markdown文件" in str(e).lower():
                    # 尝试从错误信息中提取项目名称
                    try:
                        from project_matcher import ProjectMatcher
                        matcher = ProjectMatcher(str(processor.file_manager.base_dir))
                        best_match = matcher.get_best_match(project_name)
                        if best_match:
                            feedback.show_markdown_not_found_help(best_match['name'])
                        else:
                            feedback.show_project_not_found_help(project_name)
                    except:
                        print(f"❌ 文件不存在: {e}")
                else:
                    print(f"❌ 文件不存在: {e}")
                sys.exit(1)

            except RuntimeError as e:
                if "AI服务未初始化" in str(e):
                    from user_feedback import UserFeedback
                    feedback = UserFeedback(processor.file_manager)
                    feedback.show_ai_service_not_configured_help()
                else:
                    print(f"❌ 运行时错误: {e}")
                sys.exit(1)

            except Exception as e:
                print(f"❌ 生成Anki闪卡失败: {e}")
                logging.error(f"生成Anki闪卡失败: {e}", exc_info=True)
                sys.exit(1)

        elif sys.argv[1] == '--md-to-anki':
            # 直接将Markdown转换为Anki
            if len(sys.argv) < 3:
                print("请指定Markdown文件路径")
                sys.exit(1)

            md_file = sys.argv[2].strip().strip('"').strip("'")
            try:
                result = processor.process_markdown_to_anki(md_file)
                print(f"\n✅ Markdown转Anki完成!")
                print(f"输入文件: {result['input_file']}")
                print(f"输出文件: {result['output_file']}")
                print(f"总卡片数: {result['total_cards']}")
                print(f"包含图片的卡片: {result['questions_with_images']}")
                print(f"图片总数: {result['total_images']}")
            except Exception as e:
                print(f"❌ Markdown转Anki失败: {e}")
                sys.exit(1)

        elif sys.argv[1] == '--full':
            # 完整流程：文档→MD→Anki
            if len(sys.argv) < 3:
                print("请指定文档文件路径")
                sys.exit(1)

            file_path = sys.argv[2].strip().strip('"').strip("'")
            try:
                # 第一步：处理文档为Markdown
                print("🔄 第一步：处理文档为Markdown...")
                doc_result = processor.process_document(file_path)
                print(f"✅ 文档处理完成: {doc_result['project_name']}")

                # 第二步：生成Anki闪卡
                print("🔄 第二步：生成Anki闪卡...")
                anki_result = processor.generate_anki_for_project(doc_result['project_name'])

                print(f"\n🎉 完整流程处理完成!")
                print(f"项目名称: {anki_result['project_name']}")
                print(f"Markdown文件: {doc_result['markdown_file']}")
                print(f"Anki文件: {anki_result['output_file']}")
                print(f"总卡片数: {anki_result['total_cards']}")
                print(f"包含图片的卡片: {anki_result['questions_with_images']}")
                print(f"图片总数: {anki_result['total_images']}")

            except Exception as e:
                print(f"❌ 完整流程处理失败: {e}")
                sys.exit(1)

        elif sys.argv[1] == '--failed-stats':
            # 显示失败块统计信息
            if processor.ai_service and hasattr(processor.ai_service, 'get_failed_blocks_stats'):
                stats = processor.ai_service.get_failed_blocks_stats()
                print("\n📊 失败块统计信息")
                print("=" * 50)

                if stats['total_failed'] == 0:
                    print("✅ 没有失败的块")
                else:
                    print(f"总失败数: {stats['total_failed']}")
                    print(f"可重试数: {stats['retry_candidates']}")
                    print()

                    print("按失败类型统计:")
                    for failure_type, count in stats['by_type'].items():
                        print(f"  {failure_type}: {count}")
                    print()

                    print("按项目统计:")
                    for project, count in stats['by_project'].items():
                        print(f"  {project}: {count}")
                    print()

                    print(f"日志文件: {stats['log_file']}")
                    print(f"文件存在: {'是' if stats['file_exists'] else '否'}")
            else:
                print("\n❌ AI服务未初始化或不支持失败块管理功能")

        elif sys.argv[1] == '--failed-list':
            # 列出失败的块
            if processor.ai_service and hasattr(processor.ai_service, 'failed_block_logger'):
                failed_blocks = processor.ai_service.failed_block_logger.get_failed_blocks()
                print("\n📋 失败块列表")
                print("=" * 50)

                if not failed_blocks:
                    print("✅ 没有失败的块")
                else:
                    print(f"找到 {len(failed_blocks)} 个失败块:")
                    print()

                    for i, block in enumerate(failed_blocks, 1):
                        print(f"{i}. 块 {block.block_index} (项目: {block.project_name})")
                        print(f"   时间: {block.timestamp}")
                        print(f"   失败类型: {block.failure_type}")
                        print(f"   失败原因: {block.failure_reason}")
                        print(f"   尝试次数: {block.attempt_count}")
                        print(f"   输入长度: {block.input_length}, 输出长度: {block.output_length}")
                        if block.validation_ratio > 0:
                            print(f"   验证比例: {block.validation_ratio:.1%}")
                        if block.channel_name:
                            print(f"   通道: {block.channel_name}")
                        print()
            else:
                print("\n❌ AI服务未初始化或不支持失败块管理功能")

        elif sys.argv[1] == '--failed-retry':
            # 重试失败的块
            if processor.ai_service and hasattr(processor.ai_service, 'get_retry_candidates'):
                retry_candidates = processor.ai_service.get_retry_candidates()
                print("\n🔄 重试失败块")
                print("=" * 50)

                if not retry_candidates:
                    print("✅ 没有可重试的失败块")
                else:
                    print(f"找到 {len(retry_candidates)} 个可重试的块:")
                    for block in retry_candidates:
                        print(f"  块 {block.block_index} (项目: {block.project_name}) - {block.failure_reason}")

                    confirm = input(f"\n确认重试这 {len(retry_candidates)} 个块? (y/N): ").strip().lower()
                    if confirm == 'y':
                        print("\n开始重试...")
                        success_count = 0
                        for block in retry_candidates:
                            print(f"重试块 {block.block_index} (项目: {block.project_name})...")
                            try:
                                # 设置项目名称用于日志记录
                                processor.ai_service.config['project_name'] = block.project_name

                                success = processor.ai_service.call_api(
                                    chunk=block.input_content,
                                    idx=block.block_index,
                                    cache_dir=None  # 不保存到缓存，避免覆盖
                                )

                                if success:
                                    print(f"✓ 块 {block.block_index} 重试成功")
                                    success_count += 1
                                else:
                                    print(f"✗ 块 {block.block_index} 重试失败")

                            except Exception as e:
                                print(f"✗ 块 {block.block_index} 重试异常: {e}")

                        print(f"\n重试完成: {success_count}/{len(retry_candidates)} 个块成功")
                    else:
                        print("取消重试")
            else:
                print("\n❌ AI服务未初始化或不支持失败块管理功能")

        elif sys.argv[1] == '--failed-clear':
            # 清理失败块日志
            if processor.ai_service and hasattr(processor.ai_service, 'failed_block_logger'):
                stats = processor.ai_service.get_failed_blocks_stats()
                print("\n🗑️ 清理失败块日志")
                print("=" * 50)

                if stats['total_failed'] == 0:
                    print("✅ 没有失败块需要清理")
                else:
                    print(f"将清理 {stats['total_failed']} 条失败记录")
                    confirm = input("确认清理所有失败块记录? (y/N): ").strip().lower()
                    if confirm == 'y':
                        removed_count = processor.ai_service.failed_block_logger.clear_log()
                        print(f"✅ 已清理 {removed_count} 条记录")
                    else:
                        print("取消清理")
            else:
                print("\n❌ AI服务未初始化或不支持失败块管理功能")

        elif sys.argv[1] == '--chunking-stats':
            # 显示项目的分块统计信息
            if len(sys.argv) < 3:
                print("请指定项目名称")
                sys.exit(1)

            project_name = sys.argv[2]

            if processor.semantic_chunker and processor.chunk_processor:
                try:
                    # 查找项目的预处理文件
                    base_dir = Path(processor.config['BASE_DIR'])
                    preprocessed_file_path = processor.semantic_chunker.get_preprocessed_file_path(project_name, base_dir)

                    if preprocessed_file_path.exists():
                        content = processor.semantic_chunker.load_preprocessed_content(preprocessed_file_path)
                        if content:
                            stats = processor.chunk_processor.get_chunk_statistics(content)

                            print(f"\n📊 项目 '{project_name}' 的语义分块统计")
                            print("=" * 50)
                            print(f"总内容长度: {stats['total_content_length']:,} 字符")
                            print(f"分块标记数量: {stats['chunk_marker_count']}")
                            print(f"有效语义块: {stats['effective_chunks']}")
                            print(f"分块标记: {stats['chunk_marker']}")
                            print()

                            validation = stats['validation']
                            print("分块质量验证:")
                            print(f"  验证状态: {'✅ 通过' if validation['valid'] else '❌ 失败'}")
                            print(f"  验证信息: {validation['reason']}")
                            print(f"  平均块长度: {validation['avg_length']:.0f} 字符")
                            print(f"  最短块: {validation['min_length']} 字符")
                            print(f"  最长块: {validation['max_length']} 字符")
                            print()

                            dist = validation['length_distribution']
                            print("长度分布:")
                            print(f"  短块 (<1000字符): {dist['short']}")
                            print(f"  中等块 (1000-5000字符): {dist['medium']}")
                            print(f"  长块 (>5000字符): {dist['long']}")
                        else:
                            print(f"❌ 无法读取项目 '{project_name}' 的预处理文件")
                    else:
                        print(f"❌ 项目 '{project_name}' 没有预处理文件")
                        print("提示: 请先使用智能分块模式处理该项目")

                except Exception as e:
                    print(f"❌ 获取分块统计失败: {e}")
            else:
                print("\n❌ 智能分块功能未初始化")

        elif sys.argv[1] == '--chunking-preview':
            # 预览项目的语义分块
            if len(sys.argv) < 3:
                print("请指定项目名称")
                sys.exit(1)

            project_name = sys.argv[2]

            if processor.semantic_chunker and processor.chunk_processor:
                try:
                    # 查找项目的预处理文件
                    base_dir = Path(processor.config['BASE_DIR'])
                    preprocessed_file_path = processor.semantic_chunker.get_preprocessed_file_path(project_name, base_dir)

                    if preprocessed_file_path.exists():
                        content = processor.semantic_chunker.load_preprocessed_content(preprocessed_file_path)
                        if content:
                            previews = processor.chunk_processor.preview_chunks(content, 150)

                            print(f"\n👁️ 项目 '{project_name}' 的语义分块预览")
                            print("=" * 50)

                            for preview in previews:
                                print(f"块 {preview['index'] + 1}:")
                                print(f"  长度: {preview['length']} 字符, {preview['lines']} 行")
                                print(f"  预览: {preview['preview']}")
                                print()
                        else:
                            print(f"❌ 无法读取项目 '{project_name}' 的预处理文件")
                    else:
                        print(f"❌ 项目 '{project_name}' 没有预处理文件")
                        print("提示: 请先使用智能分块模式处理该项目")

                except Exception as e:
                    print(f"❌ 预览分块失败: {e}")
            else:
                print("\n❌ 智能分块功能未初始化")

        elif sys.argv[1] == '--chunking-enable':
            # 启用智能分块模式
            try:
                # 更新配置文件
                config_file = Path("config.ini")
                if config_file.exists():
                    with open(config_file, 'r', encoding='utf-8') as f:
                        content = f.read()

                    # 替换ENABLE_SMART_CHUNKING设置
                    import re
                    pattern = r'ENABLE_SMART_CHUNKING\s*=\s*\w+'
                    if re.search(pattern, content):
                        content = re.sub(pattern, 'ENABLE_SMART_CHUNKING = true', content)
                    else:
                        # 如果没有找到，添加到配置文件
                        content += '\nENABLE_SMART_CHUNKING = true\n'

                    with open(config_file, 'w', encoding='utf-8') as f:
                        f.write(content)

                    print("✅ 已启用智能分块模式")
                    print("提示: 重新运行处理命令将使用两阶段语义分块")
                else:
                    print("❌ 配置文件不存在")
            except Exception as e:
                print(f"❌ 启用智能分块失败: {e}")

        elif sys.argv[1] == '--chunking-disable':
            # 禁用智能分块模式
            try:
                # 更新配置文件
                config_file = Path("config.ini")
                if config_file.exists():
                    with open(config_file, 'r', encoding='utf-8') as f:
                        content = f.read()

                    # 替换ENABLE_SMART_CHUNKING设置
                    import re
                    pattern = r'ENABLE_SMART_CHUNKING\s*=\s*\w+'
                    if re.search(pattern, content):
                        content = re.sub(pattern, 'ENABLE_SMART_CHUNKING = false', content)
                    else:
                        # 如果没有找到，添加到配置文件
                        content += '\nENABLE_SMART_CHUNKING = false\n'

                    with open(config_file, 'w', encoding='utf-8') as f:
                        f.write(content)

                    print("✅ 已禁用智能分块模式")
                    print("提示: 将使用传统字符分块模式")
                else:
                    print("❌ 配置文件不存在")
            except Exception as e:
                print(f"❌ 禁用智能分块失败: {e}")

        else:
            # 处理单个文档
            file_path = sys.argv[1]
            # 规范化路径，处理路径中的引号和特殊字符
            file_path = file_path.strip().strip('"').strip("'")
            result = processor.process_document(file_path)
            
            print("\n文档处理完成!")
            print(f"项目名称: {result['project_name']}")
            print(f"文档类型: {result['document_type'].upper()}")
            print(f"原始文件: {result['original_file']}")
            print(f"Markdown文件: {result['markdown_file']}")
            if 'image_count' in result:
                print(f"图片数量: {result['image_count']}")
            if 'total_pages' in result:
                print(f"总页数: {result['total_pages']}")
            if 'paragraph_count' in result:
                print(f"段落数: {result['paragraph_count']}")
    
    except Exception as e:
        print(f"处理失败: {e}")
        logging.error(f"处理失败: {e}", exc_info=True)
        sys.exit(1)


if __name__ == "__main__":
    main()
