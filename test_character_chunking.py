#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试字符基础分块功能
Test character-based chunking functionality
"""

import sys
from pathlib import Path

# 添加当前目录到路径，以便导入模块
sys.path.insert(0, str(Path(__file__).parent))

from md_to_anki import make_chunks, find_best_boundary


def test_find_best_boundary():
    """测试边界查找功能"""
    print("=== 测试边界查找功能 ===")
    
    # 测试文本
    text = "这是第一句话。这是第二句话！\n\n这是新段落的开始。这里有一些内容 继续写一些文字。"
    
    # 测试不同的理想位置
    test_cases = [
        (20, "在句子中间"),
        (10, "在词中间"),
        (25, "在句号后"),
        (30, "在段落分隔符附近"),
    ]
    
    for ideal_pos, description in test_cases:
        boundary = find_best_boundary(text, ideal_pos, ideal_pos + 10)
        print(f"{description}: 理想位置 {ideal_pos}, 实际边界 {boundary}")
        print(f"  边界字符: '{text[boundary-1:boundary+1] if boundary > 0 and boundary < len(text) else 'N/A'}'")
        print(f"  前文: '{text[max(0, boundary-10):boundary]}'")
        print(f"  后文: '{text[boundary:boundary+10]}'")
        print()


def test_character_chunking():
    """测试字符基础分块"""
    print("=== 测试字符基础分块 ===")
    
    # 创建测试文本
    test_lines = [
        "这是第一行文本，包含一些内容。",
        "这是第二行，也有一些文字。",
        "第三行比较长一些，包含更多的文字内容，用来测试分块功能是否正常工作。",
        "第四行。",
        "第五行包含一些特殊字符：！@#$%^&*()。",
        "",  # 空行
        "第六行在空行后面。",
        "第七行继续添加内容。",
        "第八行是最后一行，用来完成测试。"
    ]
    
    # 测试不同的参数组合
    test_cases = [
        (100, 80, "小块大重叠"),
        (200, 150, "中等块中等重叠"),
        (50, 40, "很小块小重叠"),
        (1000, 800, "大块大重叠（超过文本长度）"),
    ]
    
    full_text = "\n".join(test_lines)
    print(f"原始文本长度: {len(full_text)} 字符")
    print(f"原始文本: {repr(full_text[:100])}...")
    print()
    
    for chunk_size, chunk_stride, description in test_cases:
        print(f"--- {description} (块大小: {chunk_size}, 步长: {chunk_stride}) ---")
        
        chunks = make_chunks(test_lines, chunk_size, chunk_stride)
        
        print(f"生成 {len(chunks)} 个块:")
        for i, (idx, text) in enumerate(chunks):
            print(f"  块 {idx}: {len(text)} 字符")
            print(f"    开始: {repr(text[:30])}...")
            print(f"    结束: ...{repr(text[-30:])}")
            
            # 检查重叠
            if i > 0:
                prev_text = chunks[i-1][1]
                # 寻找重叠部分
                overlap_found = False
                for j in range(min(len(prev_text), len(text))):
                    if prev_text[-j-1:] == text[:j+1]:
                        if j > 0:
                            print(f"    重叠: {j+1} 字符 - '{text[:j+1]}'")
                            overlap_found = True
                        break
                if not overlap_found:
                    print(f"    重叠: 未检测到重叠")
        print()


def test_edge_cases():
    """测试边界情况"""
    print("=== 测试边界情况 ===")
    
    # 测试空文本
    print("1. 空文本:")
    chunks = make_chunks([], 100, 80)
    print(f"   结果: {len(chunks)} 个块")
    print()
    
    # 测试单行短文本
    print("2. 单行短文本:")
    short_lines = ["短文本"]
    chunks = make_chunks(short_lines, 100, 80)
    print(f"   结果: {len(chunks)} 个块")
    if chunks:
        print(f"   内容: {repr(chunks[0][1])}")
    print()
    
    # 测试单行长文本
    print("3. 单行长文本:")
    long_lines = ["这是一行很长的文本" * 20]  # 重复20次
    chunks = make_chunks(long_lines, 100, 80)
    print(f"   结果: {len(chunks)} 个块")
    for i, (idx, text) in enumerate(chunks[:3]):  # 只显示前3个
        print(f"   块 {idx}: {len(text)} 字符")
    print()


if __name__ == "__main__":
    print("字符基础分块功能测试")
    print("=" * 50)
    
    test_find_best_boundary()
    test_character_chunking()
    test_edge_cases()
    
    print("测试完成！")
