#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
retry_failed_blocks.py

失败块重试工具 - 提供命令行界面管理和重试失败的AI处理块
"""

import argparse
import json
import logging
import sys
from pathlib import Path
from typing import List, Dict, Any

from failed_block_logger import FailedBlockLogger
from content_validator import ContentValidator
from ai_service import AIService
from file_manager import FileManager


class FailedBlockRetryTool:
    """失败块重试工具"""
    
    def __init__(self, config_file: str = "config.ini"):
        """初始化重试工具"""
        self.file_manager = FileManager(config_file)
        self.config = self.file_manager.config
        
        # 初始化失败块日志记录器
        failed_blocks_log = self.config.get('FAILED_BLOCKS_LOG', 'failed_blocks.log')
        self.failed_block_logger = FailedBlockLogger(failed_blocks_log)
        
        # 初始化AI服务（如果需要重试）
        self.ai_service = None
        if self._has_ai_config():
            try:
                self.ai_service = AIService(self.config)
                logging.info("AI服务初始化成功")
            except Exception as e:
                logging.error(f"AI服务初始化失败: {e}")
    
    def _has_ai_config(self) -> bool:
        """检查是否有AI配置"""
        return 'channels' in self.config or all(
            key in self.config for key in ['api_base_url', 'api_key', 'model_name']
        )
    
    def list_failed_blocks(self, project_name: str = None, failure_type: str = None) -> None:
        """列出失败的块"""
        print("=== 失败块列表 ===")
        
        if failure_type:
            failed_blocks = self.failed_block_logger.get_failed_blocks_by_type(failure_type, project_name)
        else:
            failed_blocks = self.failed_block_logger.get_failed_blocks(project_name)
        
        if not failed_blocks:
            print("没有找到失败的块")
            return
        
        print(f"找到 {len(failed_blocks)} 个失败块:")
        print()
        
        for i, block in enumerate(failed_blocks, 1):
            print(f"{i}. 块 {block.block_index} (项目: {block.project_name})")
            print(f"   时间: {block.timestamp}")
            print(f"   失败类型: {block.failure_type}")
            print(f"   失败原因: {block.failure_reason}")
            print(f"   尝试次数: {block.attempt_count}")
            print(f"   输入长度: {block.input_length}, 输出长度: {block.output_length}")
            if block.validation_ratio > 0:
                print(f"   验证比例: {block.validation_ratio:.1%}")
            if block.channel_name:
                print(f"   通道: {block.channel_name}")
            print()
    
    def show_statistics(self, project_name: str = None) -> None:
        """显示失败块统计信息"""
        print("=== 失败块统计 ===")
        
        stats = self.failed_block_logger.get_statistics(project_name)
        
        if stats['total_failed'] == 0:
            print("没有失败的块")
            return
        
        print(f"总失败数: {stats['total_failed']}")
        print(f"可重试数: {stats['retry_candidates']}")
        print()
        
        print("按失败类型统计:")
        for failure_type, count in stats['by_type'].items():
            print(f"  {failure_type}: {count}")
        print()
        
        print("按项目统计:")
        for project, count in stats['by_project'].items():
            print(f"  {project}: {count}")
        print()
        
        print(f"日志文件: {stats['log_file']}")
        print(f"文件存在: {'是' if stats['file_exists'] else '否'}")
    
    def retry_blocks(self, project_name: str = None, block_indices: List[int] = None, 
                    failure_types: List[str] = None, interactive: bool = True) -> None:
        """重试失败的块"""
        if not self.ai_service:
            print("错误: AI服务未初始化，无法重试")
            return
        
        print("=== 重试失败块 ===")
        
        # 获取要重试的块
        if block_indices:
            # 重试指定索引的块
            all_failed = self.failed_block_logger.get_failed_blocks(project_name)
            retry_blocks = [block for block in all_failed if block.block_index in block_indices]
        elif failure_types:
            # 重试指定类型的块
            retry_blocks = []
            for failure_type in failure_types:
                retry_blocks.extend(
                    self.failed_block_logger.get_failed_blocks_by_type(failure_type, project_name)
                )
        else:
            # 重试所有可重试的块
            retry_blocks = self.failed_block_logger.get_retry_candidates(project_name)
        
        if not retry_blocks:
            print("没有找到可重试的块")
            return
        
        print(f"找到 {len(retry_blocks)} 个可重试的块")
        
        if interactive:
            # 交互式确认
            print("\n要重试的块:")
            for i, block in enumerate(retry_blocks, 1):
                print(f"{i}. 块 {block.block_index} (项目: {block.project_name}) - {block.failure_reason}")
            
            confirm = input(f"\n确认重试这 {len(retry_blocks)} 个块? (y/N): ").strip().lower()
            if confirm != 'y':
                print("取消重试")
                return
        
        # 执行重试
        success_count = 0
        for block in retry_blocks:
            print(f"\n重试块 {block.block_index} (项目: {block.project_name})...")
            
            try:
                # 调用AI服务重新处理
                success = self.ai_service.call_api(
                    chunk=block.input_content,
                    idx=block.block_index,
                    cache_dir=None  # 不保存到缓存，避免覆盖
                )
                
                if success:
                    print(f"✓ 块 {block.block_index} 重试成功")
                    success_count += 1
                else:
                    print(f"✗ 块 {block.block_index} 重试失败")
                    
            except Exception as e:
                print(f"✗ 块 {block.block_index} 重试异常: {e}")
        
        print(f"\n重试完成: {success_count}/{len(retry_blocks)} 个块成功")
    
    def clear_failed_blocks(self, project_name: str = None, interactive: bool = True) -> None:
        """清理失败块日志"""
        print("=== 清理失败块日志 ===")
        
        stats = self.failed_block_logger.get_statistics(project_name)
        if stats['total_failed'] == 0:
            print("没有失败块需要清理")
            return
        
        if project_name:
            message = f"清理项目 '{project_name}' 的 {stats['total_failed']} 条失败记录"
        else:
            message = f"清理所有 {stats['total_failed']} 条失败记录"
        
        if interactive:
            confirm = input(f"{message}? (y/N): ").strip().lower()
            if confirm != 'y':
                print("取消清理")
                return
        
        removed_count = self.failed_block_logger.clear_log(project_name)
        print(f"已清理 {removed_count} 条记录")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="失败块重试工具")
    parser.add_argument("--config", default="config.ini", help="配置文件路径")
    parser.add_argument("--project", help="项目名称过滤")
    parser.add_argument("--log-level", default="INFO", choices=["DEBUG", "INFO", "WARNING", "ERROR"],
                       help="日志级别")
    
    subparsers = parser.add_subparsers(dest="command", help="可用命令")
    
    # list 命令
    list_parser = subparsers.add_parser("list", help="列出失败的块")
    list_parser.add_argument("--type", help="按失败类型过滤")
    
    # stats 命令
    subparsers.add_parser("stats", help="显示失败块统计信息")
    
    # retry 命令
    retry_parser = subparsers.add_parser("retry", help="重试失败的块")
    retry_parser.add_argument("--blocks", type=int, nargs="+", help="指定要重试的块索引")
    retry_parser.add_argument("--types", nargs="+", help="指定要重试的失败类型")
    retry_parser.add_argument("--no-interactive", action="store_true", help="非交互模式")
    
    # clear 命令
    clear_parser = subparsers.add_parser("clear", help="清理失败块日志")
    clear_parser.add_argument("--no-interactive", action="store_true", help="非交互模式")
    
    args = parser.parse_args()
    
    # 设置日志级别
    logging.basicConfig(
        level=getattr(logging, args.log_level),
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    if not args.command:
        parser.print_help()
        return
    
    # 创建重试工具
    try:
        tool = FailedBlockRetryTool(args.config)
    except Exception as e:
        print(f"错误: 初始化失败 - {e}")
        sys.exit(1)
    
    # 执行命令
    try:
        if args.command == "list":
            tool.list_failed_blocks(args.project, args.type)
        elif args.command == "stats":
            tool.show_statistics(args.project)
        elif args.command == "retry":
            tool.retry_blocks(
                project_name=args.project,
                block_indices=args.blocks,
                failure_types=args.types,
                interactive=not args.no_interactive
            )
        elif args.command == "clear":
            tool.clear_failed_blocks(
                project_name=args.project,
                interactive=not args.no_interactive
            )
    except KeyboardInterrupt:
        print("\n操作被用户取消")
    except Exception as e:
        logging.error(f"执行命令时出错: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
