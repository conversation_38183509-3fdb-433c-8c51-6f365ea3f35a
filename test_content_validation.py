#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
test_content_validation.py

测试内容验证和失败块日志功能
"""

import logging
import json
from pathlib import Path
from content_validator import ContentValidator
from failed_block_logger import FailedBlockLogger

def test_content_validator():
    """测试内容验证器"""
    print("=== 测试内容验证器 ===")
    
    # 创建验证器
    config = {
        'CONTENT_VALIDATION_THRESHOLD': 0.8,
        'MAX_CONTENT_RETRIES': 2,
        'ENABLE_CONTENT_VALIDATION': 'true'
    }
    
    validator = ContentValidator.from_config(config)
    print(f"验证器配置: {validator.get_validation_stats()}")
    
    # 测试用例
    test_cases = [
        {
            'name': '正常情况',
            'input': '这是一个测试输入文本，包含足够的内容用于验证。' * 10,
            'output': '这是AI生成的输出文本，长度适中。' * 8,
            'expected': True
        },
        {
            'name': '输出过短',
            'input': '这是一个测试输入文本，包含足够的内容用于验证。' * 10,
            'output': '短输出',
            'expected': False
        },
        {
            'name': '输出为空',
            'input': '这是一个测试输入文本。',
            'output': '',
            'expected': False
        },
        {
            'name': '输入为空',
            'input': '',
            'output': '',
            'expected': True
        }
    ]
    
    for case in test_cases:
        result = validator.validate_content_length(case['input'], case['output'])
        status = "✓" if result.is_valid == case['expected'] else "✗"
        print(f"{status} {case['name']}: {result.reason}")
        print(f"   输入长度: {result.input_length}, 输出长度: {result.output_length}, 比例: {result.ratio:.2%}")
    
    # 测试重试逻辑
    print("\n=== 测试重试逻辑 ===")
    for attempt in range(5):
        retry_info = validator.get_retry_info(attempt)
        can_retry = "可重试" if retry_info['can_retry'] else "不可重试"
        print(f"尝试 {attempt}: {can_retry}, 剩余重试: {retry_info['remaining_retries']}")

def test_failed_block_logger():
    """测试失败块日志记录器"""
    print("\n=== 测试失败块日志记录器 ===")
    
    # 创建日志记录器
    log_file = "test_failed_blocks.log"
    logger = FailedBlockLogger(log_file)
    
    # 清理之前的测试数据
    logger.clear_log()
    
    # 记录一些测试失败块
    test_failures = [
        {
            'block_index': 1,
            'project_name': 'test_project_1',
            'input_content': '测试输入内容1' * 50,
            'output_content': '短输出1',
            'failure_reason': '输出长度不足: 30% < 80%',
            'failure_type': 'validation',
            'validation_ratio': 0.3
        },
        {
            'block_index': 2,
            'project_name': 'test_project_1',
            'input_content': '测试输入内容2' * 30,
            'output_content': '',
            'failure_reason': '响应内容为空',
            'failure_type': 'api_error',
            'validation_ratio': 0.0
        },
        {
            'block_index': 3,
            'project_name': 'test_project_2',
            'input_content': '测试输入内容3' * 40,
            'output_content': '部分输出3',
            'failure_reason': '网络超时',
            'failure_type': 'timeout',
            'validation_ratio': 0.5
        }
    ]
    
    for failure in test_failures:
        logger.log_failed_block(**failure)
    
    # 获取统计信息
    stats = logger.get_statistics()
    print(f"总失败数: {stats['total_failed']}")
    print(f"按类型统计: {stats['by_type']}")
    print(f"按项目统计: {stats['by_project']}")
    print(f"可重试数量: {stats['retry_candidates']}")
    
    # 获取特定项目的失败块
    project1_failures = logger.get_failed_blocks('test_project_1')
    print(f"\ntest_project_1 的失败块数量: {len(project1_failures)}")
    
    # 获取可重试的失败块
    retry_candidates = logger.get_retry_candidates()
    print(f"可重试的失败块数量: {len(retry_candidates)}")
    for candidate in retry_candidates:
        print(f"  块 {candidate.block_index}: {candidate.failure_type} - {candidate.failure_reason}")
    
    # 清理测试文件
    removed_count = logger.clear_log()
    print(f"\n清理了 {removed_count} 条测试记录")

def test_integration():
    """测试集成功能"""
    print("\n=== 测试集成功能 ===")
    
    # 模拟AI服务配置
    config = {
        'CONTENT_VALIDATION_THRESHOLD': 0.8,
        'MAX_CONTENT_RETRIES': 2,
        'ENABLE_CONTENT_VALIDATION': 'true',
        'FAILED_BLOCKS_LOG': 'test_integration_failed_blocks.log',
        'project_name': 'integration_test'
    }
    
    # 创建验证器和日志记录器
    validator = ContentValidator.from_config(config)
    logger = FailedBlockLogger(config['FAILED_BLOCKS_LOG'])
    logger.clear_log()
    
    # 模拟处理流程
    test_chunks = [
        {
            'idx': 1,
            'input': '这是一个包含丰富内容的测试文本块。' * 20,
            'output': '这是AI生成的详细回答。' * 15,  # 足够长，应该通过验证
        },
        {
            'idx': 2,
            'input': '这是另一个测试文本块。' * 25,
            'output': '短回答',  # 太短，应该失败
        }
    ]
    
    for chunk in test_chunks:
        validation_result = validator.validate_content_length(chunk['input'], chunk['output'])
        
        if validation_result.is_valid:
            print(f"块 {chunk['idx']}: 验证通过 - {validation_result.reason}")
        else:
            print(f"块 {chunk['idx']}: 验证失败 - {validation_result.reason}")
            
            # 记录失败块
            logger.log_failed_block(
                block_index=chunk['idx'],
                project_name=config['project_name'],
                input_content=chunk['input'],
                output_content=chunk['output'],
                failure_reason=validation_result.reason,
                failure_type='validation',
                attempt_count=1,
                validation_ratio=validation_result.ratio
            )
    
    # 显示最终统计
    final_stats = logger.get_statistics()
    print(f"\n最终统计: {json.dumps(final_stats, ensure_ascii=False, indent=2)}")
    
    # 清理测试文件
    logger.clear_log()

if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
    
    test_content_validator()
    test_failed_block_logger()
    test_integration()
    
    print("\n=== 所有测试完成 ===")
