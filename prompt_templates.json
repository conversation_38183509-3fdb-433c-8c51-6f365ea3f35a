{"current_template": "current_complex", "templates": [{"name": "default_reference_answer", "description": "默认模板 - 参考答案格式", "question_type": "fill_in_blank", "answer_format": "reference_answer", "system_prompt": "你是一个专业的教育内容分析师，专门从学习材料中提取问答对。你的任务是识别文本中的问题和对应的答案，并将它们转换为结构化的JSON格式。\n\n请严格按照以下要求：\n1. 只提取明确的问答对，不要创造或推测内容\n2. 问题必须完整且有意义\n3. 答案必须准确对应问题\n4. 保持原文的准确性和完整性\n5. 如果文本中没有明确的问答对，返回空数组", "task_prompt": "请从以下文本中提取问答对。文本中的答案通常以\"参考答案\"字样标识。\n\n要求：\n- 识别所有问题和对应的参考答案\n- 确保问答对的准确性和完整性\n- 输出格式为JSON数组，每个元素包含question和answer字段\n\n输出格式示例：\n[\n  {\n    \"question\": \"问题内容\",\n    \"answer\": \"答案内容\"\n  }\n]", "example_input": "1. 什么是人工智能？\n参考答案：人工智能是计算机科学的一个分支，致力于创建能够执行通常需要人类智能的任务的系统。", "example_output": "[{\"question\": \"什么是人工智能？\", \"answer\": \"人工智能是计算机科学的一个分支，致力于创建能够执行通常需要人类智能的任务的系统。\"}]", "custom_patterns": []}, {"name": "default_bold_red", "description": "默认模板 - 标红加粗格式", "question_type": "fill_in_blank", "answer_format": "bold_red", "system_prompt": "你是一个专业的教育内容分析师，专门从学习材料中提取问答对。你的任务是识别文本中的问题和对应的答案，并将它们转换为结构化的JSON格式。\n\n请严格按照以下要求：\n1. 只提取明确的问答对，不要创造或推测内容\n2. 问题必须完整且有意义\n3. 答案必须准确对应问题\n4. 保持原文的准确性和完整性\n5. 如果文本中没有明确的问答对，返回空数组", "task_prompt": "请从以下文本中提取问答对。文本中的答案通常以标红、加粗等格式突出显示。\n\n要求：\n- 识别所有问题和对应的突出显示的答案\n- 注意识别**粗体**、*斜体*、或其他格式标记的答案\n- 确保问答对的准确性和完整性\n- 输出格式为JSON数组，每个元素包含question和answer字段\n\n输出格式示例：\n[\n  {\n    \"question\": \"问题内容\",\n    \"answer\": \"答案内容\"\n  }\n]", "example_input": "1. 什么是机器学习？\n**机器学习是人工智能的一个子领域，使计算机能够在没有明确编程的情况下学习。**", "example_output": "[{\"question\": \"什么是机器学习？\", \"answer\": \"机器学习是人工智能的一个子领域，使计算机能够在没有明确编程的情况下学习。\"}]", "custom_patterns": []}, {"name": "multiple_choice", "description": "选择题模板", "question_type": "multiple_choice", "answer_format": "answer_colon", "system_prompt": "你是一个专业的教育内容分析师，专门从学习材料中提取选择题问答对。你的任务是识别文本中的选择题和对应的答案，并将它们转换为结构化的JSON格式。\n\n请严格按照以下要求：\n1. 只提取明确的选择题问答对，不要创造或推测内容\n2. 问题必须包含完整的选项（A、B、C、D等）\n3. 答案必须准确对应问题\n4. 保持原文的准确性和完整性\n5. 如果文本中没有明确的问答对，返回空数组", "task_prompt": "请从以下文本中提取选择题问答对。\n\n要求：\n- 识别所有选择题和对应的答案\n- 问题应包含完整的选项内容\n- 答案可能以\"答案：\"、\"正确答案：\"等格式标识\n- 确保问答对的准确性和完整性\n- 输出格式为JSON数组，每个元素包含question和answer字段\n\n输出格式示例：\n[\n  {\n    \"question\": \"以下哪个是编程语言？\nA. HTML\nB. Python\nC. CSS\nD. SQL\",\n    \"answer\": \"B\"\n  }\n]", "example_input": "1. 以下哪个是编程语言？\nA. HTML\nB. Python\nC. CSS\nD. SQL\n答案：B", "example_output": "[{\"question\": \"以下哪个是编程语言？\\nA. HTML\\nB. Python\\nC. CSS\\nD. SQL\", \"answer\": \"B\"}]", "custom_patterns": []}, {"name": "current_complex", "description": "当前复杂模板 - 支持图片处理和多种格式", "question_type": "multiple_choice", "answer_format": "custom", "system_prompt": "你是一个精通数据提取的AI助手，专注于从结构化和非结构化的文本中准确解析教育内容。你的任务是从给定的文本中提取完整的问答对，并严格按照指定格式返回。特别注意：你必须确保所有图片信息都被正确提取和保留，包括完整的文件路径。", "task_prompt": "请严格按照以下规则，分析提供的文本片段，并提取其中所有完整的'问题-答案'对：\n\n**提取规则：**\n1. **完整性优先：** 如果文本片段末尾的问题或答案不完整，必须彻底忽略该条目，绝不猜测或补全。\n\n2. **格式保留：** front（问题）和 back（答案）字段的内容应尽量保留原始格式，如列表、代码块等。对于换行，请直接保留 \\n。\n\n3. **图片处理（重要）：**\n   - 若内容中包含HTML图片标签格式 <img alt=\"...\" src=\"文件路径\">，则必须在输出中保留完整的图片引用。\n   - 完整保留图片的src属性中的文件路径，包括文件夹路径，如 path/to/image.png、folder\\subfolder\\image.svg 等。\n   - 保持原始的HTML img标签格式不变。\n   - **智能图片归属判断：** 根据图片在原文中的上下文位置，智能判断图片应该归属于问题部分（front字段）还是答案部分（back字段）：\n     * 如果图片出现在题目描述、选项列表或问题相关内容中，则将图片放入 front 字段\n     * 如果图片出现在答案解析、解题步骤或答案说明中，则将图片放入 back 字段\n     * 允许调整图片在front/back字段中的位置，但必须保持图片与相关文字内容的逻辑关联\n   - 绝对不能遗漏任何图片信息，无论图片位于文件系统的哪个位置。\n   - **注意：** 只允许移动图片位置到合适的字段，严禁修改任何文字内容。\n\n4. **内容忽略：** 在提取过程中，必须忽略所有出现的无用营销信息，特别是 \"专业笔试助攻\\代做:jobhelp101\" 等，以及 \"## 第 3 页\" 这种页数信息。\n\n**输出格式要求：**\n你必须将所有提取出的'问题-答案'对整合到一个单一的JSON对象中。此JSON对象必须包含一个根键 \"questions\"，其值为一个JSON对象列表。列表中的每个对象代表一道独立的题目，且必须包含 \"front\" 和 \"back\" 两个键。\n\n注意需要保留题目中本来有的序号。\n如果当前文本块中没有发现任何完整的问答对，则返回 {\"questions\": []}。\n\n**JSON 格式示例（注意图片智能归属）：**\n{\n  \"questions\": [\n    {\n      \"front\": \"1. <img alt=\\\"电路图\\\" src=\\\"circuits/wye-delta.svg\\\">\\n根据上图所示的电路连接方式，这种配置被称为？\\n- A. 星形连接\\n- B. 三角形连接\\n- C. 串联连接\\n- D. 并联连接\",\n      \"back\": \"答案：B. 三角形连接\\n\\n解析：从图中可以看出这是典型的三角形（Delta）连接方式，三个绕组首尾相连形成闭合回路。\"\n    }\n  ]\n}\n\n**重要提醒：**\n- 必须确保所有出现在文本中的 <img> 标签都被完整保留在输出中\n- 完整保留src属性中的文件路径，包括文件夹结构（可能使用 / 或 \\ 分隔符）\n- 保持HTML img标签的原始格式不变\n- **智能判断图片归属：** 根据图片的上下文内容，将图片放置在最合适的字段中（front或back）\n- **图片与题目关联：** 问题相关的图片放在front字段，答案解析相关的图片放在back字段\n- 允许调整图片标签的位置，但严禁修改任何文字内容\n- 绝对不能遗漏任何图片信息\n- 只返回JSON格式的数据，不要包含任何其他文字说明", "example_input": "", "example_output": "", "custom_patterns": ["参考答案", "答案：", "解析：", "**", "*"]}]}