#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
test_ai_chunking_refactor.py

测试重构后的AI驱动语义分块功能
"""

import logging
import sys
from pathlib import Path
from ai_service import AIService
from semantic_chunker import <PERSON>manticChunker
from multi_channel_config import MultiChannelConfigParser

def test_ai_chunking_refactor():
    """测试重构后的AI驱动语义分块功能"""
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    
    print("=== 测试AI驱动语义分块重构 ===")
    
    try:
        # 初始化配置和AI服务
        config_parser = MultiChannelConfigParser()
        config = config_parser.get_config()
        
        print(f"配置加载成功，AI分块设置:")
        print(f"  启用AI分块: {config.get('ENABLE_AI_CHUNKING', 'true')}")
        print(f"  目标块大小: {config.get('AI_CHUNKING_TARGET_SIZE', '12000')}")
        print(f"  最大内容大小: {config.get('AI_CHUNKING_MAX_CONTENT_SIZE', '50000')}")
        print(f"  超时设置: {config.get('AI_CHUNKING_TIMEOUT', '180')}")
        print(f"  回退到规则: {config.get('AI_CHUNKING_FALLBACK_TO_RULES', 'true')}")
        
        # 初始化AI服务
        ai_service = AIService(config)
        print(f"AI服务初始化成功，可用通道: {len(ai_service.channels)}")
        
        # 创建语义分块器
        chunker = SemanticChunker(ai_service, config=config)
        print(f"语义分块器初始化成功，模式: {'AI驱动' if chunker.enable_ai_chunking else '规则驱动'}")
        
        # 创建测试内容（足够长以触发分块）
        test_content = create_test_content()
        print(f"测试内容长度: {len(test_content)} 字符")
        
        # 测试AI驱动分块
        print("\n--- 测试AI驱动分块 ---")
        result = chunker.preprocess_document(test_content, "test_ai_chunking")
        
        if result.success:
            print(f"✅ AI分块成功:")
            print(f"  原始长度: {result.original_length}")
            print(f"  处理后长度: {result.preprocessed_length}")
            print(f"  分块数量: {result.chunk_count}")
            print(f"  处理时间: {result.processing_time:.2f}s")
            
            # 显示分块标记位置
            lines = result.preprocessed_content.split('\n')
            marker_positions = []
            for i, line in enumerate(lines):
                if chunker.chunk_marker in line:
                    marker_positions.append(i + 1)
            
            if marker_positions:
                print(f"  分块标记位置: {marker_positions}")
            else:
                print("  未找到分块标记")
                
            # 保存结果用于检查
            output_file = Path("test_ai_chunking_result.md")
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(result.preprocessed_content)
            print(f"  结果已保存到: {output_file}")
            
        else:
            print(f"❌ AI分块失败: {result.error_message}")
        
        # 测试规则驱动分块（作为对比）
        print("\n--- 测试规则驱动分块（对比） ---")
        config_rule_based = config.copy()
        config_rule_based['ENABLE_AI_CHUNKING'] = 'false'
        chunker_rule = SemanticChunker(ai_service, config=config_rule_based)
        
        result_rule = chunker_rule.preprocess_document(test_content, "test_rule_chunking")
        
        if result_rule.success:
            print(f"✅ 规则分块成功:")
            print(f"  分块数量: {result_rule.chunk_count}")
            print(f"  处理时间: {result_rule.processing_time:.2f}s")
        else:
            print(f"❌ 规则分块失败: {result_rule.error_message}")
        
        # 比较结果
        if result.success and result_rule.success:
            print(f"\n--- 结果比较 ---")
            print(f"AI分块数量: {result.chunk_count}")
            print(f"规则分块数量: {result_rule.chunk_count}")
            print(f"AI处理时间: {result.processing_time:.2f}s")
            print(f"规则处理时间: {result_rule.processing_time:.2f}s")
        
        print("\n=== 测试完成 ===")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        logging.error(f"测试异常: {e}", exc_info=True)
        return False
    
    return True

def create_test_content():
    """创建测试内容"""
    content = """# 人工智能学习指南

## 第一章 机器学习基础

### 1.1 什么是机器学习？

机器学习是人工智能的一个重要分支，它使计算机能够在没有明确编程的情况下学习和改进。

**问题1**: 机器学习的定义是什么？
**答案**: 机器学习是一种使计算机系统能够自动学习和改进的方法，无需明确编程。它通过算法分析数据，识别模式，并做出预测或决策。

### 1.2 机器学习的类型

机器学习主要分为三种类型：监督学习、无监督学习和强化学习。

**问题2**: 监督学习和无监督学习的区别是什么？
**答案**: 监督学习使用标记的训练数据来学习输入和输出之间的映射关系，而无监督学习处理未标记的数据，寻找隐藏的模式和结构。

## 第二章 深度学习

### 2.1 神经网络基础

神经网络是深度学习的基础，它模拟人脑神经元的工作方式。

**问题3**: 神经网络的基本组成部分有哪些？
**答案**: 神经网络由神经元（节点）、权重、偏置和激活函数组成。神经元接收输入，通过权重和偏置进行计算，然后通过激活函数产生输出。

### 2.2 深度学习的应用

深度学习在图像识别、自然语言处理、语音识别等领域有广泛应用。

**问题4**: 深度学习在计算机视觉中的主要应用有哪些？
**答案**: 深度学习在计算机视觉中的主要应用包括图像分类、目标检测、图像分割、人脸识别、医学图像分析等。

## 第三章 自然语言处理

### 3.1 文本预处理

文本预处理是自然语言处理的重要步骤，包括分词、去停用词、词干提取等。

**问题5**: 为什么需要进行文本预处理？
**答案**: 文本预处理可以清理和标准化文本数据，去除噪声，提取有用信息，为后续的分析和建模提供高质量的输入数据。

### 3.2 词向量表示

词向量是将词语转换为数值向量的技术，使计算机能够理解和处理文本。

**问题6**: Word2Vec和BERT在词向量表示上有什么区别？
**答案**: Word2Vec生成静态词向量，每个词只有一个固定的向量表示；而BERT生成上下文相关的动态词向量，同一个词在不同上下文中有不同的向量表示。

## 第四章 强化学习

### 4.1 强化学习基础

强化学习是通过与环境交互来学习最优策略的机器学习方法。

**问题7**: 强化学习的核心要素有哪些？
**答案**: 强化学习的核心要素包括智能体（Agent）、环境（Environment）、状态（State）、动作（Action）、奖励（Reward）和策略（Policy）。

### 4.2 Q学习算法

Q学习是一种无模型的强化学习算法，通过学习动作-价值函数来找到最优策略。

**问题8**: Q学习算法的更新公式是什么？
**答案**: Q学习的更新公式是：Q(s,a) = Q(s,a) + α[r + γ*max(Q(s',a')) - Q(s,a)]，其中α是学习率，γ是折扣因子。

## 第五章 实践项目

### 5.1 项目规划

在开始AI项目之前，需要明确问题定义、数据收集、模型选择等关键步骤。

**问题9**: AI项目的典型流程包括哪些步骤？
**答案**: AI项目的典型流程包括：问题定义、数据收集和预处理、特征工程、模型选择和训练、模型评估和优化、部署和监控。

### 5.2 模型评估

模型评估是确保AI系统性能的重要环节，需要使用合适的评估指标。

**问题10**: 分类问题常用的评估指标有哪些？
**答案**: 分类问题常用的评估指标包括准确率（Accuracy）、精确率（Precision）、召回率（Recall）、F1分数、AUC-ROC曲线等。

## 总结

人工智能是一个快速发展的领域，掌握机器学习、深度学习、自然语言处理和强化学习的基础知识对于AI从业者至关重要。通过不断学习和实践，可以在这个激动人心的领域取得成功。
"""
    
    # 重复内容以确保足够长度触发分块（需要超过6000字符）
    return content * 5  # 增加重复次数以确保超过6000字符阈值

if __name__ == "__main__":
    success = test_ai_chunking_refactor()
    sys.exit(0 if success else 1)
