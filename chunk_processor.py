#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
chunk_processor.py

分块处理器 - 实现两阶段处理的第二阶段
Stage 2: 处理预标记的语义分块，生成Anki卡片
"""

import logging
import time
from pathlib import Path
from typing import List, Dict, Any, Tuple
from dataclasses import dataclass
from concurrent.futures import ThreadPoolExecutor, as_completed
from tqdm import tqdm


@dataclass
class ProcessingResult:
    """处理结果数据类"""
    success: bool
    total_chunks: int
    processed_chunks: int
    failed_chunks: int
    processing_time: float
    error_message: str = ""
    cache_dir: str = ""


class ChunkProcessor:
    """
    分块处理器
    
    处理带有语义分块标记的内容，为每个语义块生成Anki卡片
    """
    
    def __init__(self, ai_service, chunk_marker: str = "<!-- CHUNK_BREAK -->"):
        """
        初始化分块处理器
        
        Args:
            ai_service: AI服务实例
            chunk_marker: 分块标记字符串
        """
        self.ai_service = ai_service
        self.chunk_marker = chunk_marker
        
        logging.info(f"分块处理器初始化完成，分块标记: {self.chunk_marker}")
    
    def split_by_markers(self, content: str) -> List[str]:
        """
        根据分块标记分割内容
        
        Args:
            content: 带有分块标记的内容
            
        Returns:
            List[str]: 分割后的内容块列表
        """
        # 按分块标记分割
        chunks = content.split(self.chunk_marker)
        
        # 清理空白块和过短的块
        cleaned_chunks = []
        for chunk in chunks:
            chunk = chunk.strip()
            if chunk and len(chunk) > 100:  # 过滤过短的块
                cleaned_chunks.append(chunk)
        
        logging.info(f"内容分割完成: 原始{len(chunks)}块 -> 有效{len(cleaned_chunks)}块")
        
        return cleaned_chunks
    
    def process_semantic_chunks(self, content: str, project_name: str, 
                              project_config: Dict[str, Any]) -> ProcessingResult:
        """
        处理语义分块内容
        
        Args:
            content: 带有分块标记的内容
            project_name: 项目名称
            project_config: 项目配置
            
        Returns:
            ProcessingResult: 处理结果
        """
        start_time = time.time()
        
        logging.info(f"开始处理语义分块: 项目={project_name}")
        
        # 分割内容
        chunks = self.split_by_markers(content)
        
        if not chunks:
            return ProcessingResult(
                success=False,
                total_chunks=0,
                processed_chunks=0,
                failed_chunks=0,
                processing_time=time.time() - start_time,
                error_message="没有有效的内容块",
                cache_dir=project_config.get('CACHE_DIR', '')
            )
        
        # 设置缓存目录
        cache_dir = Path(project_config['CACHE_DIR'])
        cache_dir.mkdir(exist_ok=True)
        
        # 设置项目名称用于失败块日志记录
        if hasattr(self.ai_service, 'config'):
            self.ai_service.config['project_name'] = project_name
        
        # 创建语义块索引映射
        semantic_chunks = [(i, chunk) for i, chunk in enumerate(chunks)]
        
        logging.info(f"准备处理 {len(semantic_chunks)} 个语义块...")
        
        # 计算并发数
        max_workers = self._calculate_max_workers()
        
        # 并发处理语义块
        success_count = 0
        failed_count = 0
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交需要处理的任务（跳过已缓存的）
            futures = {}
            for idx, chunk_content in semantic_chunks:
                cache_file = cache_dir / f"semantic_{idx}.json"
                if not cache_file.exists():
                    future = executor.submit(
                        self.ai_service.call_api, 
                        chunk_content, 
                        f"semantic_{idx}",  # 使用特殊前缀标识语义块
                        str(cache_dir)
                    )
                    futures[future] = idx
            
            # 显示进度并等待结果
            if futures:
                logging.info(f"需要处理 {len(futures)} 个新语义块，{len(semantic_chunks) - len(futures)} 个块已缓存")
                
                for fut in tqdm(as_completed(futures), total=len(futures), desc="语义块处理进度"):
                    idx = futures[fut]
                    try:
                        success = fut.result()
                        if success:
                            success_count += 1
                        else:
                            failed_count += 1
                    except Exception as e:
                        logging.error(f"语义块 {idx} 处理异常: {e}")
                        failed_count += 1
                
                logging.info(f"语义块处理完成: {success_count}/{len(futures)} 个块成功处理")
            else:
                logging.info("所有语义块都已缓存，跳过 AI 处理")
                success_count = len(semantic_chunks)
        
        processing_time = time.time() - start_time
        total_processed = success_count + len(semantic_chunks) - len(futures)  # 包括已缓存的
        
        return ProcessingResult(
            success=failed_count == 0,
            total_chunks=len(semantic_chunks),
            processed_chunks=total_processed,
            failed_chunks=failed_count,
            processing_time=processing_time,
            cache_dir=str(cache_dir)
        )
    
    def _calculate_max_workers(self) -> int:
        """计算最大并发工作进程数"""
        total_max_concurrent = 0

        if hasattr(self.ai_service, 'channels'):
            for channel in self.ai_service.channels:
                if channel.enabled:
                    total_max_concurrent += channel.max_concurrent
        else:
            # 单通道模式，使用默认值
            total_max_concurrent = 10

        # 确保至少有1个工作进程
        return max(1, min(total_max_concurrent, 20))
    
    def validate_semantic_chunks(self, chunks: List[str]) -> Dict[str, Any]:
        """
        验证语义块的质量
        
        Args:
            chunks: 语义块列表
            
        Returns:
            Dict: 验证结果
        """
        if not chunks:
            return {
                'valid': False,
                'reason': '没有有效的语义块',
                'chunk_count': 0,
                'avg_length': 0,
                'min_length': 0,
                'max_length': 0
            }
        
        lengths = [len(chunk) for chunk in chunks]
        avg_length = sum(lengths) / len(lengths)
        min_length = min(lengths)
        max_length = max(lengths)
        
        # 验证规则
        valid = True
        reasons = []
        
        # 检查块数量
        if len(chunks) < 1:
            valid = False
            reasons.append("语义块数量过少")
        
        # 检查平均长度
        if avg_length < 500:
            valid = False
            reasons.append(f"平均块长度过短: {avg_length:.0f}字符")
        
        # 检查最小长度
        if min_length < 100:
            valid = False
            reasons.append(f"存在过短的块: {min_length}字符")
        
        # 检查最大长度
        if max_length > 20000:
            reasons.append(f"存在过长的块: {max_length}字符（建议进一步分割）")
        
        return {
            'valid': valid,
            'reason': '; '.join(reasons) if reasons else '验证通过',
            'chunk_count': len(chunks),
            'avg_length': avg_length,
            'min_length': min_length,
            'max_length': max_length,
            'length_distribution': {
                'short': len([l for l in lengths if l < 1000]),
                'medium': len([l for l in lengths if 1000 <= l < 5000]),
                'long': len([l for l in lengths if l >= 5000])
            }
        }
    
    def get_chunk_statistics(self, content: str) -> Dict[str, Any]:
        """
        获取分块统计信息
        
        Args:
            content: 带有分块标记的内容
            
        Returns:
            Dict: 统计信息
        """
        chunks = self.split_by_markers(content)
        validation = self.validate_semantic_chunks(chunks)
        
        return {
            'total_content_length': len(content),
            'chunk_marker_count': content.count(self.chunk_marker),
            'effective_chunks': len(chunks),
            'validation': validation,
            'chunk_marker': self.chunk_marker
        }
    
    def preview_chunks(self, content: str, max_preview_length: int = 200) -> List[Dict[str, Any]]:
        """
        预览分块结果
        
        Args:
            content: 带有分块标记的内容
            max_preview_length: 最大预览长度
            
        Returns:
            List[Dict]: 分块预览信息
        """
        chunks = self.split_by_markers(content)
        
        previews = []
        for i, chunk in enumerate(chunks):
            preview_text = chunk[:max_preview_length]
            if len(chunk) > max_preview_length:
                preview_text += "..."
            
            previews.append({
                'index': i,
                'length': len(chunk),
                'preview': preview_text,
                'lines': chunk.count('\n') + 1
            })
        
        return previews


if __name__ == "__main__":
    # 测试代码
    logging.basicConfig(level=logging.INFO)
    
    # 模拟AI服务
    class MockAIService:
        def __init__(self):
            self.config = {}
            self.channels = []
        
        def call_api(self, chunk, idx, cache_dir):
            # 模拟处理时间
            time.sleep(0.1)
            return True
    
    # 创建分块处理器
    ai_service = MockAIService()
    processor = ChunkProcessor(ai_service)
    
    # 测试内容
    test_content = """# 第一部分

这是第一部分的内容，包含一些问题和答案。

## 问题1
这是第一个问题？

这是第一个问题的答案。

<!-- CHUNK_BREAK -->

# 第二部分

这是第二部分的内容。

## 问题2
这是第二个问题？

这是第二个问题的答案。

<!-- CHUNK_BREAK -->

# 第三部分

这是第三部分的内容。

## 问题3
这是第三个问题？

这是第三个问题的答案。"""
    
    # 测试分割
    chunks = processor.split_by_markers(test_content)
    print(f"分割结果: {len(chunks)} 个块")
    
    # 测试统计
    stats = processor.get_chunk_statistics(test_content)
    print(f"统计信息: {stats}")
    
    # 测试预览
    previews = processor.preview_chunks(test_content, 100)
    print("\n分块预览:")
    for preview in previews:
        print(f"块 {preview['index']}: {preview['length']}字符")
        print(f"  预览: {preview['preview']}")
        print()
