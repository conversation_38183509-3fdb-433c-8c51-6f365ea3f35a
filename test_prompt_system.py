#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
test_prompt_system.py

测试提示词管理系统的功能
"""

import sys
import os
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s [%(levelname)s] %(message)s')

def test_prompt_manager():
    """测试提示词管理器"""
    print("🧪 测试提示词管理器...")
    
    try:
        from prompt_manager import PromptManager
        
        # 创建提示词管理器
        manager = PromptManager()
        
        # 列出所有模板
        templates = manager.list_templates()
        print(f"✅ 找到 {len(templates)} 个提示词模板:")
        for template in templates:
            current_mark = " (当前)" if template['is_current'] else ""
            print(f"  - {template['name']}: {template['description']}{current_mark}")
            print(f"    类型: {template['question_type']}, 格式: {template['answer_format']}")
        
        # 测试切换模板
        if len(templates) > 1:
            first_template = templates[0]['name']
            second_template = templates[1]['name']
            
            print(f"\n🔄 测试切换模板: {first_template} -> {second_template}")
            success = manager.set_current_template(second_template)
            if success:
                print(f"✅ 成功切换到模板: {second_template}")
                
                # 验证切换结果
                current = manager.get_current_template()
                if current and current.name == second_template:
                    print(f"✅ 验证成功，当前模板: {current.name}")
                else:
                    print("❌ 验证失败，模板切换未生效")
            else:
                print(f"❌ 切换模板失败: {second_template}")
        
        return True
        
    except Exception as e:
        print(f"❌ 提示词管理器测试失败: {e}")
        return False

def test_document_processor_with_prompt():
    """测试带提示词的文档处理器"""
    print("\n🧪 测试带提示词的文档处理器...")
    
    try:
        from document_processor import DocumentProcessor
        
        # 测试不指定提示词模板
        print("📝 测试默认提示词模板...")
        processor1 = DocumentProcessor()
        if processor1.ai_service:
            current_template = processor1.ai_service.get_current_template_info()
            if current_template:
                print(f"✅ 默认模板: {current_template['name']} - {current_template['description']}")
            else:
                print("⚠️  未设置默认模板")
        else:
            print("⚠️  AI服务未初始化（可能缺少配置）")
        
        # 测试指定提示词模板
        print("\n📝 测试指定提示词模板...")
        templates = processor1.ai_service.get_prompt_templates() if processor1.ai_service else []
        if templates and len(templates) > 1:
            test_template = templates[1]['name']  # 选择第二个模板
            print(f"🎯 指定使用模板: {test_template}")
            
            processor2 = DocumentProcessor(prompt_template=test_template)
            if processor2.ai_service:
                current_template = processor2.ai_service.get_current_template_info()
                if current_template and current_template['name'] == test_template:
                    print(f"✅ 成功使用指定模板: {current_template['name']}")
                else:
                    print(f"❌ 模板设置失败，当前模板: {current_template['name'] if current_template else 'None'}")
            else:
                print("⚠️  AI服务未初始化")
        else:
            print("⚠️  模板数量不足，跳过指定模板测试")
        
        return True
        
    except Exception as e:
        print(f"❌ 文档处理器测试失败: {e}")
        return False

def test_cli_arguments():
    """测试命令行参数解析"""
    print("\n🧪 测试命令行参数解析...")
    
    try:
        # 模拟命令行参数
        original_argv = sys.argv.copy()
        
        # 测试 --list-prompts
        print("📝 测试 --list-prompts 参数...")
        sys.argv = ['document_processor.py', '--list-prompts']
        
        # 这里我们不能直接调用main()，因为它会退出程序
        # 但我们可以测试参数解析逻辑
        if '--list-prompts' in sys.argv:
            print("✅ --list-prompts 参数识别成功")
        
        # 测试 --prompt 参数
        print("📝 测试 --prompt 参数...")
        sys.argv = ['document_processor.py', '--anki', 'test_project', '--prompt', 'default_reference_answer']
        
        if '--prompt' in sys.argv:
            prompt_index = sys.argv.index('--prompt')
            if prompt_index + 1 < len(sys.argv):
                prompt_template = sys.argv[prompt_index + 1]
                print(f"✅ --prompt 参数识别成功，模板: {prompt_template}")
            else:
                print("❌ --prompt 参数缺少模板名称")
        
        # 恢复原始参数
        sys.argv = original_argv
        
        return True
        
    except Exception as e:
        print(f"❌ 命令行参数测试失败: {e}")
        sys.argv = original_argv
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试提示词管理系统")
    print("=" * 50)
    
    results = []
    
    # 测试提示词管理器
    results.append(test_prompt_manager())
    
    # 测试文档处理器
    results.append(test_document_processor_with_prompt())
    
    # 测试命令行参数
    results.append(test_cli_arguments())
    
    # 总结测试结果
    print("\n" + "=" * 50)
    print("📊 测试结果总结:")
    
    success_count = sum(results)
    total_count = len(results)
    
    if success_count == total_count:
        print(f"🎉 所有测试通过! ({success_count}/{total_count})")
        print("\n✅ 提示词管理系统实现完成，功能包括:")
        print("  - 多提示词模板支持")
        print("  - 命令行参数选择提示词")
        print("  - GUI界面提示词选择")
        print("  - 向后兼容性保持")
    else:
        print(f"⚠️  部分测试失败: {success_count}/{total_count}")
        print("请检查失败的测试项目")
    
    return success_count == total_count

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
