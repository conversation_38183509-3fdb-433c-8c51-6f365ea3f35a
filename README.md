# PDF题库转Anki工具

专业级Python脚本，将Markdown格式题库高效转换为Anki兼容的制表符分隔文本文件。

## 功能特性

- 🚀 **高并发处理**：支持多线程并发调用AI API
- 📊 **流式处理**：支持流式API调用，解决大块内容503错误
- 🔄 **智能缓存**：避免重复处理，支持断点续传
- 🛡️ **健壮异常处理**：多重容错机制，确保处理稳定性
- 📝 **完整日志**：详细的处理日志，便于调试和监控
- ⚙️ **灵活配置**：支持多种AI API提供商

## 项目结构

```
├── md_to_anki.py          # 主脚本（流式处理版本）
├── extract_pdf_md.py      # PDF转Markdown工具
├── config.ini             # 配置文件
├── requirements.txt       # Python依赖
├── cache/                 # API调用缓存目录
├── tiku_md/              # Markdown文件目录
│   ├── tiku.md           # 题库文件
│   └── images/           # 图片资源
└── test_*.py             # 测试脚本

备份文件：
├── md_to_anki_old.py     # 原版备份
└── md_to_anki_main.py    # 主版本备份
```

## 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 配置API

# 使用方法
1. 复制模板：`cp config.sample.ini config.ini`
2. 填入你的 API key 等私密信息。
3. 确保 `config.ini` 已加入 `.gitignore`。

### 3. 运行转换

```bash
python md_to_anki.py
```

## 配置说明

| 参数 | 说明 | 默认值 |
|------|------|--------|
| `INPUT_FILE` | 输入的Markdown文件路径 | `tiku.md` |
| `OUTPUT_FILE` | 输出的Anki文件路径 | `ankiflashcards.txt` |
| `LINES_TO_SKIP` | 跳过文件开头的行数 | `13` |
| `CHUNK_SIZE` | 每个文本块的行数 | `500` |
| `CHUNK_STRIDE` | 滑动窗口步长 | `450` |
| `MAX_WORKERS` | 最大并发线程数 | `10` |
| `REQUEST_TIMEOUT` | API请求超时时间(秒) | `120` |
| `API_PROVIDER` | API提供商 | `siliconflow` |

## 支持的API提供商

- **SiliconFlow** (`siliconflow`)
- **OpenAI** (`openai`)
- 其他兼容OpenAI格式的API

## 输出格式

生成的Anki文件格式：
```
#separator:tab
#html:true
#tags column:3
问题内容	答案内容
```

## 日志和缓存

- **日志文件**：`script.log`
- **缓存目录**：`cache/` - 存储API调用结果，支持断点续传
- **输出文件**：`ankiflashcards.txt` - 可直接导入Anki

## 故障排除

### 503 服务器错误
- 脚本使用流式处理，已解决大块内容导致的503问题
- 如仍有问题，可减小 `CHUNK_SIZE` 参数

### JSON解析错误
- 脚本具备自动修复机制
- 会尝试正则表达式备用提取

### API超时
- 增加 `REQUEST_TIMEOUT` 参数
- 减少 `MAX_WORKERS` 并发数

## 测试脚本

项目包含多个测试脚本：
- `test_md_to_anki.py` - 主功能测试
- `test_direct_stream.py` - 流式API测试
- `test_api_connection.py` - API连通性测试

运行测试：
```bash
python test_md_to_anki.py
```

## 版本说明

当前版本为流式处理增强版本，相比原版具有：
- 更强的流式处理能力
- 支持 `reasoning_content` 字段
- JSON自动修复机制
- 正则表达式备用提取
- 更详细的错误日志

---

**注意**：请确保API密钥安全，不要将包含敏感信息的配置文件提交到版本控制系统。

## 🆕 最新功能：智能图片位置优化

### PDF图片位置分析
- **边界框分析**：精确提取PDF中图片和文本的位置信息
- **智能关系判断**：分析图片与周围文本的相对位置关系
- **合理布局生成**：根据位置关系智能插入图片到最合适的位置

### 支持的位置关系类型
- `between_paragraphs`: 图片位于两段文字之间
- `after_paragraph`: 图片位于某段文字下方  
- `before_paragraph`: 图片位于某段文字上方
- `inline`: 图片与文字重叠（内嵌图片）
- `standalone`: 独立位置的图片

详细说明请参考：[IMAGE_POSITION_OPTIMIZATION.md](IMAGE_POSITION_OPTIMIZATION.md)
