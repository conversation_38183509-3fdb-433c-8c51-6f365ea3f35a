# 多文档题库生成系统 - 使用指南

## 🎯 系统概述

本系统是一个专业的多文档处理和Anki闪卡生成解决方案，支持PDF、Word等多种文档格式的智能提取和转换。系统采用项目化管理方式，为每个文档创建独立的处理环境，确保文件组织的清晰性和可管理性。

## ✅ 主要改进

### 相比原系统的优势：

1. **多格式支持**: 新增Word文档处理能力
2. **智能命名**: 统一的文件命名和项目管理
3. **分类存储**: 按功能分类存储各种文件
4. **批量处理**: 支持目录级别的批量处理
5. **项目管理**: 完整的项目生命周期管理
6. **完全兼容**: 保持与原系统的100%兼容性

## 🚀 快速上手

### 1. 处理单个PDF文档
```bash
python document_processor.py your_document.pdf
```

### 2. 处理单个Word文档
```bash
python document_processor.py your_document.docx
```

### 3. 批量处理目录
```bash
python document_processor.py --dir ./documents_folder
```

### 4. 查看所有项目
```bash
python document_processor.py --list
```

### 5. 查看统计摘要
```bash
python document_processor.py --summary
```

## 📋 详细工作流程

### 步骤1: 文档处理
文档处理后会生成以下结构：
```
documents/
├── extracted/
│   └── [项目名]/
│       ├── [文档名].md      # 提取的内容
│       └── images/          # 图片文件
├── cache/
│   └── [项目名]/            # 处理缓存
└── logs/
    └── [项目名].log         # 处理日志
```

### 步骤2: Anki闪卡生成
1. 选择要处理的项目
2. 修改`config.ini`中的`INPUT_FILE`
3. 运行`python md_to_anki.py`

## 🔧 配置说明

### config.ini 关键配置项

```ini
[DEFAULT]
# 文档处理
BASE_DIR = documents
PDF_TOP_MARGIN = 50.0
PDF_BOTTOM_MARGIN = 50.0

# AI API
API_BASE_URL = https://your-api-endpoint/v1
API_KEY = your-api-key
MODEL_NAME = your-model-name

# 处理参数 (Character-based chunking)
CHUNK_SIZE = 12000      # 每个文本块的字符数 (characters per chunk)
CHUNK_STRIDE = 9500     # 滑动窗口步长 (characters step size, creates 2500 char overlap)
# MAX_WORKERS 已移除 - 现在并发数由各通道的 MAX_CONCURRENT 总和决定
```

## 📁 项目命名规范

### 自动生成的项目名格式：
`{清理后的文件名}_{时间戳}`

### 示例：
- `textbook.pdf` → `textbook_20240614_143052`
- `我的笔记.docx` → `我的笔记_20240614_143052`
- `Data File (v2).pdf` → `Data_File_v2_20240614_143052`

### 图片命名格式：
`{项目名}_{来源}_{页码}_{索引}.png`

## 🛠️ 高级用法

### 编程接口使用

```python
from document_processor import DocumentProcessor

# 创建处理器
processor = DocumentProcessor(base_dir="my_workspace")

# 处理单个文档
result = processor.process_document("document.pdf")
print(f"项目: {result['project_name']}")
print(f"图片: {result['image_count']}")

# 批量处理
results = processor.process_directory("./docs")
success_count = sum(1 for r in results if 'error' not in r)
print(f"成功处理: {success_count}/{len(results)}")

# 项目管理
projects = processor.file_manager.list_projects()
summary = processor.get_processing_summary()
```

### 自定义PDF提取参数

```python
from pdf_extractor import extract_pdf_to_markdown

result = extract_pdf_to_markdown(
    "document.pdf",
    base_dir="custom_workspace",
    top_margin=80,    # 更大的顶部边距
    bottom_margin=60  # 更大的底部边距
)
```

## 📊 项目管理

### 查看项目状态
```bash
python document_processor.py --list
```

输出示例：
```
共有 3 个项目:
  - textbook_20240614_143052 (45张图片, 已缓存, 已生成Anki)
  - notes_20240614_150830 (12张图片, 已缓存)
  - reference_20240614_160215 (已缓存)
```

### 项目清理
```python
from file_manager import FileManager

fm = FileManager("documents")
# 清理项目，保留Anki文件
fm.cleanup_project("old_project_name", keep_anki=True)
```

## 🔄 从原系统迁移

### 继续使用原有工作流：
```bash
# 原有的PDF提取方式仍然有效
python extract_pdf_md.py tiku.pdf

# 原有的Anki生成方式仍然有效
python md_to_anki.py
```

### 迁移到新系统：
1. 使用新的处理器处理文档
2. 更新配置文件中的路径
3. 享受更好的文件组织和管理

## 🚨 故障排除

### 常见问题及解决方案：

#### 1. Word文档处理失败
```bash
pip install python-docx
```

#### 2. 图片提取失败
```bash
pip install PyMuPDF Pillow
```

#### 3. 项目目录混乱
- 检查`BASE_DIR`配置是否正确
- 确保有足够的磁盘空间
- 验证文件权限

#### 4. API调用问题
- 检查网络连接
- 验证API密钥和端点
- 调整并发参数

### 日志查看：
```bash
# 查看特定项目日志
cat documents/logs/project_name.log

# 查看演示日志
cat demo.log

# 查看主程序日志（原系统兼容）
cat script.log
```

## 💡 最佳实践

### 1. 文档准备
- 确保PDF文档质量良好
- Word文档格式规范
- 文件名使用标准字符

### 2. 批量处理
- 按文档类型分组处理
- 合理设置并发参数
- 定期清理不需要的项目

### 3. Anki生成
- 根据文档长度调整分块大小
- 监控API使用量
- 及时备份生成的闪卡

### 4. 项目管理
- 定期查看项目摘要
- 清理过期的缓存文件
- 保持目录结构整洁

## 🔮 未来规划

### 计划中的功能：
- [ ] PowerPoint文档支持
- [ ] Excel表格处理
- [ ] 图片OCR识别
- [ ] 自动化工作流
- [ ] Web界面管理
- [ ] 云存储集成

## 📞 技术支持

### 问题反馈：
- 查看详细日志文件
- 运行演示脚本验证功能
- 检查依赖安装情况

### 性能优化：
- 根据系统资源调整并发数
- 合理设置分块大小
- 定期清理缓存文件

---

**提示**: 建议先运行 `python demo_multi_document.py` 来体验完整功能。
