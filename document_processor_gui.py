#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
document_processor_gui.py

GUI应用程序，为document_processor.py提供用户友好的图形界面
支持所有命令行功能，包括单文档处理、批量处理、项目管理和Anki生成
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import threading
import queue
import os
import sys
import configparser
from pathlib import Path
from typing import Dict, List, Optional, Any
import logging
from datetime import datetime

# 导入后端处理模块
from document_processor import DocumentProcessor
from file_manager import FileManager


class DocumentProcessorGUI:
    """文档处理器GUI主类"""
    
    def __init__(self):
        """初始化GUI应用程序"""
        self.root = tk.Tk()
        self.root.title("文档处理器 - Document Processor GUI")
        self.root.geometry("1000x700")
        self.root.minsize(800, 600)
        
        # 设置应用程序图标（如果有的话）
        try:
            # 可以在这里设置应用程序图标
            # self.root.iconbitmap("icon.ico")
            pass
        except:
            pass
        
        # 创建消息队列用于线程间通信
        self.message_queue = queue.Queue()

        # 当前处理状态
        self.processing = False
        self.current_operation = None

        # 创建GUI组件
        self.create_widgets()
        self.setup_layout()
        self.setup_menu()

        # 初始化后端处理器（在GUI组件创建后）
        self.processor = None
        self.init_processor()

        # 启动消息处理循环
        self.process_queue()

        # 设置窗口关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

        # 设置拖拽功能
        self.setup_drag_and_drop()

        # 加载设置
        self.load_settings_from_config()
    
    def init_processor(self):
        """初始化文档处理器"""
        try:
            # 获取选择的提示词模板
            prompt_template = getattr(self, 'selected_prompt_template', None)
            self.processor = DocumentProcessor(prompt_template=prompt_template)
            self.log_message("文档处理器初始化成功")
        except Exception as e:
            self.log_message(f"文档处理器初始化失败: {e}", level="ERROR")
            messagebox.showerror("初始化错误", f"文档处理器初始化失败:\n{e}")
    
    def create_widgets(self):
        """创建GUI组件"""
        # 创建主框架
        self.main_frame = ttk.Frame(self.root)
        
        # 创建笔记本控件（标签页）
        self.notebook = ttk.Notebook(self.main_frame)
        
        # 创建各个标签页
        self.create_single_doc_tab()
        self.create_batch_processing_tab()
        self.create_project_management_tab()
        self.create_markdown_to_anki_tab()
        self.create_settings_tab()
        
        # 创建状态栏和日志区域
        self.create_status_area()
    
    def create_single_doc_tab(self):
        """创建单文档处理标签页"""
        self.single_doc_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.single_doc_frame, text="单文档处理")
        
        # 文件选择区域
        file_frame = ttk.LabelFrame(self.single_doc_frame, text="文件选择", padding=10)
        file_frame.pack(fill="x", padx=10, pady=5)
        
        self.selected_file_var = tk.StringVar()
        ttk.Label(file_frame, text="选择文档文件:").pack(anchor="w")
        
        file_select_frame = ttk.Frame(file_frame)
        file_select_frame.pack(fill="x", pady=5)
        
        self.file_entry = ttk.Entry(file_select_frame, textvariable=self.selected_file_var, width=60)
        self.file_entry.pack(side="left", fill="x", expand=True)
        
        ttk.Button(file_select_frame, text="浏览...", 
                  command=self.browse_single_file).pack(side="right", padx=(5, 0))
        
        # 处理选项
        options_frame = ttk.LabelFrame(self.single_doc_frame, text="处理选项", padding=10)
        options_frame.pack(fill="x", padx=10, pady=5)
        
        self.process_to_markdown_var = tk.BooleanVar()
        self.generate_anki_var = tk.BooleanVar()

        # 设置默认值
        self.process_to_markdown_var.set(True)
        self.generate_anki_var.set(False)

        ttk.Checkbutton(options_frame, text="提取为Markdown",
                       variable=self.process_to_markdown_var).pack(anchor="w")
        ttk.Checkbutton(options_frame, text="同时生成Anki闪卡",
                       variable=self.generate_anki_var).pack(anchor="w")
        
        # 处理按钮
        button_frame = ttk.Frame(self.single_doc_frame)
        button_frame.pack(fill="x", padx=10, pady=10)
        
        self.process_single_btn = ttk.Button(button_frame, text="开始处理", 
                                           command=self.process_single_document)
        self.process_single_btn.pack(side="left")
        
        # 结果显示区域
        result_frame = ttk.LabelFrame(self.single_doc_frame, text="处理结果", padding=10)
        result_frame.pack(fill="both", expand=True, padx=10, pady=5)
        
        self.single_result_text = scrolledtext.ScrolledText(result_frame, height=10, wrap=tk.WORD)
        self.single_result_text.pack(fill="both", expand=True)
    
    def create_batch_processing_tab(self):
        """创建批量处理标签页"""
        self.batch_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.batch_frame, text="批量处理")
        
        # 目录选择区域
        dir_frame = ttk.LabelFrame(self.batch_frame, text="目录选择", padding=10)
        dir_frame.pack(fill="x", padx=10, pady=5)
        
        self.selected_dir_var = tk.StringVar()
        ttk.Label(dir_frame, text="选择包含文档的目录:").pack(anchor="w")
        
        dir_select_frame = ttk.Frame(dir_frame)
        dir_select_frame.pack(fill="x", pady=5)
        
        self.dir_entry = ttk.Entry(dir_select_frame, textvariable=self.selected_dir_var, width=60)
        self.dir_entry.pack(side="left", fill="x", expand=True)
        
        ttk.Button(dir_select_frame, text="浏览...", 
                  command=self.browse_directory).pack(side="right", padx=(5, 0))
        
        # 批量处理选项
        batch_options_frame = ttk.LabelFrame(self.batch_frame, text="批量处理选项", padding=10)
        batch_options_frame.pack(fill="x", padx=10, pady=5)
        
        self.recursive_var = tk.BooleanVar()
        self.skip_existing_var = tk.BooleanVar()

        # 设置默认值
        self.recursive_var.set(True)
        self.skip_existing_var.set(True)

        ttk.Checkbutton(batch_options_frame, text="递归处理子目录",
                       variable=self.recursive_var).pack(anchor="w")
        ttk.Checkbutton(batch_options_frame, text="跳过已处理的文件",
                       variable=self.skip_existing_var).pack(anchor="w")
        
        # 处理按钮
        batch_button_frame = ttk.Frame(self.batch_frame)
        batch_button_frame.pack(fill="x", padx=10, pady=10)
        
        self.process_batch_btn = ttk.Button(batch_button_frame, text="开始批量处理", 
                                          command=self.process_directory)
        self.process_batch_btn.pack(side="left")
        
        # 进度显示
        progress_frame = ttk.LabelFrame(self.batch_frame, text="处理进度", padding=10)
        progress_frame.pack(fill="x", padx=10, pady=5)
        
        self.batch_progress = ttk.Progressbar(progress_frame, mode='determinate')
        self.batch_progress.pack(fill="x", pady=5)
        
        self.batch_status_var = tk.StringVar(value="准备就绪")
        ttk.Label(progress_frame, textvariable=self.batch_status_var).pack(anchor="w")
        
        # 批量处理结果
        batch_result_frame = ttk.LabelFrame(self.batch_frame, text="处理结果", padding=10)
        batch_result_frame.pack(fill="both", expand=True, padx=10, pady=5)
        
        self.batch_result_text = scrolledtext.ScrolledText(batch_result_frame, height=8, wrap=tk.WORD)
        self.batch_result_text.pack(fill="both", expand=True)
    
    def create_project_management_tab(self):
        """创建项目管理标签页"""
        self.project_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.project_frame, text="项目管理")
        
        # 项目列表区域
        list_frame = ttk.LabelFrame(self.project_frame, text="项目列表", padding=10)
        list_frame.pack(fill="both", expand=True, padx=10, pady=5)
        
        # 搜索框
        search_frame = ttk.Frame(list_frame)
        search_frame.pack(fill="x", pady=(0, 10))
        
        ttk.Label(search_frame, text="搜索项目:").pack(side="left")
        self.project_search_var = tk.StringVar()
        self.project_search_var.trace_add("write", self.filter_projects)
        search_entry = ttk.Entry(search_frame, textvariable=self.project_search_var, width=30)
        search_entry.pack(side="left", padx=(5, 10))
        
        ttk.Button(search_frame, text="刷新列表", 
                  command=self.refresh_project_list).pack(side="left")
        
        # 项目列表树形控件（支持多选）
        columns = ("name", "type", "images", "status")
        self.project_tree = ttk.Treeview(list_frame, columns=columns, show="tree headings", height=12, selectmode="extended")
        
        # 设置列标题
        self.project_tree.heading("#0", text="项目名称")
        self.project_tree.heading("name", text="文件名")
        self.project_tree.heading("type", text="类型")
        self.project_tree.heading("images", text="图片数")
        self.project_tree.heading("status", text="状态")
        
        # 设置列宽
        self.project_tree.column("#0", width=300)
        self.project_tree.column("name", width=200)
        self.project_tree.column("type", width=80)
        self.project_tree.column("images", width=80)
        self.project_tree.column("status", width=120)
        
        # 添加滚动条
        project_scrollbar = ttk.Scrollbar(list_frame, orient="vertical", command=self.project_tree.yview)
        self.project_tree.configure(yscrollcommand=project_scrollbar.set)
        
        self.project_tree.pack(side="left", fill="both", expand=True)
        project_scrollbar.pack(side="right", fill="y")
        
        # 项目操作按钮
        project_button_frame = ttk.Frame(self.project_frame)
        project_button_frame.pack(fill="x", padx=10, pady=5)

        # 第一行按钮
        button_row1 = ttk.Frame(project_button_frame)
        button_row1.pack(fill="x", pady=(0, 5))

        ttk.Button(button_row1, text="查看详情",
                  command=self.view_project_details).pack(side="left", padx=(0, 5))
        ttk.Button(button_row1, text="打开文件夹",
                  command=self.open_project_folder).pack(side="left", padx=(0, 5))
        ttk.Button(button_row1, text="生成Anki闪卡",
                  command=self.generate_anki_for_selected_project).pack(side="left", padx=(0, 5))
        ttk.Button(button_row1, text="删除项目",
                  command=self.delete_project).pack(side="left", padx=(0, 5))

        # 第二行按钮（批量操作）
        button_row2 = ttk.Frame(project_button_frame)
        button_row2.pack(fill="x")

        ttk.Label(button_row2, text="批量操作:", font=("TkDefaultFont", 8, "bold")).pack(side="left", padx=(0, 10))
        ttk.Button(button_row2, text="全选",
                  command=self.select_all_projects).pack(side="left", padx=(0, 5))
        ttk.Button(button_row2, text="取消选择",
                  command=self.deselect_all_projects).pack(side="left", padx=(0, 5))
        ttk.Button(button_row2, text="批量生成Anki",
                  command=self.batch_generate_anki).pack(side="left", padx=(0, 5))

        # 选择状态显示
        self.selection_status_var = tk.StringVar(value="未选择项目")
        ttk.Label(button_row2, textvariable=self.selection_status_var,
                 font=("TkDefaultFont", 8), foreground="gray").pack(side="right")

        # 绑定选择事件以更新状态显示
        self.project_tree.bind('<<TreeviewSelect>>', self.on_project_selection_changed)
    
    def setup_layout(self):
        """设置布局"""
        self.main_frame.pack(fill="both", expand=True, padx=5, pady=5)
        self.notebook.pack(fill="both", expand=True)
    
    def setup_menu(self):
        """设置菜单栏"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # 文件菜单
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="文件", menu=file_menu)
        file_menu.add_command(label="打开文档...", command=self.browse_single_file)
        file_menu.add_command(label="打开目录...", command=self.browse_directory)
        file_menu.add_separator()
        file_menu.add_command(label="退出", command=self.on_closing)
        
        # 工具菜单
        tools_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="工具", menu=tools_menu)
        tools_menu.add_command(label="清理缓存", command=self.clear_cache)
        tools_menu.add_command(label="导出项目列表", command=self.export_project_list)
        
        # 帮助菜单
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="帮助", menu=help_menu)
        help_menu.add_command(label="使用说明", command=self.show_help)
        help_menu.add_command(label="关于", command=self.show_about)

    def create_markdown_to_anki_tab(self):
        """创建Markdown转Anki标签页"""
        self.md_anki_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.md_anki_frame, text="Markdown转Anki")

        # 说明文本
        info_frame = ttk.LabelFrame(self.md_anki_frame, text="功能说明", padding=10)
        info_frame.pack(fill="x", padx=10, pady=5)

        info_text = "此功能用于将任意Markdown文件直接转换为Anki闪卡，无需先处理为项目。\n适用于已有的Markdown笔记或从其他来源获得的Markdown文件。"
        ttk.Label(info_frame, text=info_text, wraplength=600).pack(anchor="w")

        # Markdown文件选择区域
        md_select_frame = ttk.LabelFrame(self.md_anki_frame, text="选择Markdown文件", padding=10)
        md_select_frame.pack(fill="x", padx=10, pady=5)

        ttk.Label(md_select_frame, text="Markdown文件路径:").pack(anchor="w")

        file_input_frame = ttk.Frame(md_select_frame)
        file_input_frame.pack(fill="x", pady=5)

        self.md_file_var = tk.StringVar()
        self.md_file_entry = ttk.Entry(file_input_frame, textvariable=self.md_file_var, width=60)
        self.md_file_entry.pack(side="left", fill="x", expand=True)

        ttk.Button(file_input_frame, text="浏览...",
                  command=self.browse_markdown_file).pack(side="right", padx=(5, 0))

        # 转换选项
        options_frame = ttk.LabelFrame(self.md_anki_frame, text="转换选项", padding=10)
        options_frame.pack(fill="x", padx=10, pady=5)

        self.preserve_images_var = tk.BooleanVar()
        self.preserve_images_var.set(True)  # 设置默认值
        ttk.Checkbutton(options_frame, text="保留图片引用",
                       variable=self.preserve_images_var).pack(anchor="w")

        # 转换按钮
        convert_button_frame = ttk.Frame(self.md_anki_frame)
        convert_button_frame.pack(fill="x", padx=10, pady=10)

        self.md_to_anki_btn = ttk.Button(convert_button_frame, text="开始转换",
                                       command=self.convert_markdown_to_anki)
        self.md_to_anki_btn.pack(side="left", padx=(0, 10))

        # 只预处理按钮
        self.preprocess_only_btn = ttk.Button(convert_button_frame, text="只预处理MD",
                                            command=self.preprocess_markdown_file_only)
        self.preprocess_only_btn.pack(side="left")

        # 转换结果
        result_frame = ttk.LabelFrame(self.md_anki_frame, text="转换结果", padding=10)
        result_frame.pack(fill="both", expand=True, padx=10, pady=5)

        self.md_anki_result_text = scrolledtext.ScrolledText(result_frame, height=10, wrap=tk.WORD)
        self.md_anki_result_text.pack(fill="both", expand=True)

    def create_settings_tab(self):
        """创建设置标签页"""
        self.settings_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.settings_frame, text="设置")

        # AI配置信息区域（只读显示）
        ai_frame = ttk.LabelFrame(self.settings_frame, text="AI配置信息", padding=10)
        ai_frame.pack(fill="x", padx=10, pady=5)

        # 配置文件说明
        config_info = ttk.Label(ai_frame, text="AI配置通过 config.ini 文件管理，支持多通道配置。\n请编辑 config.ini 文件来配置API密钥、模型等参数。",
                               justify="left", foreground="blue")
        config_info.pack(anchor="w", pady=5)

        # 通道管理按钮
        status_frame = ttk.Frame(ai_frame)
        status_frame.pack(fill="x", pady=5)

        ttk.Button(status_frame, text="查看AI通道状态", command=self.show_channel_status).pack(side="left")
        ttk.Button(status_frame, text="测试AI通道", command=self.show_channel_test).pack(side="left", padx=(10, 0))
        ttk.Button(status_frame, text="提示词管理", command=self.show_prompt_manager).pack(side="left", padx=(10, 0))
        ttk.Button(status_frame, text="重新加载配置", command=self.reload_config).pack(side="left", padx=(10, 0))

        # 提示词模板选择区域
        prompt_frame = ttk.LabelFrame(self.settings_frame, text="提示词模板设置", padding=10)
        prompt_frame.pack(fill="x", padx=10, pady=5)

        ttk.Label(prompt_frame, text="默认提示词模板:").grid(row=0, column=0, sticky="w", pady=2)

        # 提示词模板下拉框
        self.prompt_template_var = tk.StringVar()
        self.prompt_template_combo = ttk.Combobox(prompt_frame, textvariable=self.prompt_template_var,
                                                 state="readonly", width=30)
        self.prompt_template_combo.grid(row=0, column=1, sticky="w", padx=(5, 0), pady=2)

        # 延迟刷新提示词模板列表，等待所有组件初始化完成
        self.root.after(100, self.refresh_prompt_templates)

        # 模板信息显示
        self.prompt_info_var = tk.StringVar()
        prompt_info_label = ttk.Label(prompt_frame, textvariable=self.prompt_info_var,
                                     foreground="gray", font=("TkDefaultFont", 8))
        prompt_info_label.grid(row=1, column=0, columnspan=2, sticky="w", pady=(2, 0))

        # 绑定选择事件
        self.prompt_template_combo.bind('<<ComboboxSelected>>', self.on_prompt_template_selected)

        # 处理设置区域
        processing_frame = ttk.LabelFrame(self.settings_frame, text="处理设置", padding=10)
        processing_frame.pack(fill="x", padx=10, pady=5)

        ttk.Label(processing_frame, text="PDF顶部边距:").grid(row=0, column=0, sticky="w", pady=2)
        self.pdf_top_margin_var = tk.StringVar(value="50.0")
        ttk.Entry(processing_frame, textvariable=self.pdf_top_margin_var, width=20).grid(row=0, column=1, sticky="w", padx=(5, 0), pady=2)

        ttk.Label(processing_frame, text="PDF底部边距:").grid(row=1, column=0, sticky="w", pady=2)
        self.pdf_bottom_margin_var = tk.StringVar(value="50.0")
        ttk.Entry(processing_frame, textvariable=self.pdf_bottom_margin_var, width=20).grid(row=1, column=1, sticky="w", padx=(5, 0), pady=2)

        ttk.Label(processing_frame, text="文本块大小:").grid(row=2, column=0, sticky="w", pady=2)
        self.chunk_size_var = tk.StringVar(value="500")
        ttk.Entry(processing_frame, textvariable=self.chunk_size_var, width=20).grid(row=2, column=1, sticky="w", padx=(5, 0), pady=2)

        ttk.Label(processing_frame, text="文本块步长:").grid(row=3, column=0, sticky="w", pady=2)
        self.chunk_stride_var = tk.StringVar(value="450")
        ttk.Entry(processing_frame, textvariable=self.chunk_stride_var, width=20).grid(row=3, column=1, sticky="w", padx=(5, 0), pady=2)

        ttk.Label(processing_frame, text="跳过行数:").grid(row=4, column=0, sticky="w", pady=2)
        self.lines_to_skip_var = tk.StringVar(value="13")
        ttk.Entry(processing_frame, textvariable=self.lines_to_skip_var, width=20).grid(row=4, column=1, sticky="w", padx=(5, 0), pady=2)

        # 智能分块设置区域
        chunking_frame = ttk.LabelFrame(self.settings_frame, text="智能分块设置", padding=10)
        chunking_frame.pack(fill="x", padx=10, pady=5)

        # 启用智能分块选项
        self.enable_smart_chunking_var = tk.BooleanVar()
        ttk.Checkbutton(chunking_frame, text="启用AI驱动的智能分块（两阶段处理）",
                       variable=self.enable_smart_chunking_var,
                       command=self.on_smart_chunking_changed).grid(row=0, column=0, columnspan=2, sticky="w", pady=2)

        # 内容验证设置
        ttk.Label(chunking_frame, text="内容验证阈值:").grid(row=1, column=0, sticky="w", pady=2)
        self.validation_threshold_var = tk.StringVar(value="0.8")
        ttk.Entry(chunking_frame, textvariable=self.validation_threshold_var, width=20).grid(row=1, column=1, sticky="w", padx=(5, 0), pady=2)

        ttk.Label(chunking_frame, text="最大重试次数:").grid(row=2, column=0, sticky="w", pady=2)
        self.max_retries_var = tk.StringVar(value="2")
        ttk.Entry(chunking_frame, textvariable=self.max_retries_var, width=20).grid(row=2, column=1, sticky="w", padx=(5, 0), pady=2)

        # AI分块设置分隔线
        ttk.Separator(chunking_frame, orient='horizontal').grid(row=3, column=0, columnspan=2, sticky="ew", pady=10)

        # AI分块设置
        ttk.Label(chunking_frame, text="AI分块设置", font=('TkDefaultFont', 9, 'bold')).grid(row=4, column=0, columnspan=2, sticky="w", pady=(5, 2))

        # 启用AI分块选项
        self.enable_ai_chunking_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(chunking_frame, text="启用AI驱动的智能分块",
                       variable=self.enable_ai_chunking_var,
                       command=self.on_ai_chunking_changed).grid(row=5, column=0, columnspan=2, sticky="w", pady=2)

        # AI分块目标大小
        ttk.Label(chunking_frame, text="AI分块目标大小:").grid(row=6, column=0, sticky="w", pady=2)
        self.ai_chunking_target_size_var = tk.StringVar(value="12000")
        ttk.Entry(chunking_frame, textvariable=self.ai_chunking_target_size_var, width=20).grid(row=6, column=1, sticky="w", padx=(5, 0), pady=2)

        # AI分块最大内容大小
        ttk.Label(chunking_frame, text="AI分块最大内容:").grid(row=7, column=0, sticky="w", pady=2)
        self.ai_chunking_max_content_var = tk.StringVar(value="50000")
        ttk.Entry(chunking_frame, textvariable=self.ai_chunking_max_content_var, width=20).grid(row=7, column=1, sticky="w", padx=(5, 0), pady=2)

        # AI分块超时设置
        ttk.Label(chunking_frame, text="AI分块超时(秒):").grid(row=8, column=0, sticky="w", pady=2)
        self.ai_chunking_timeout_var = tk.StringVar(value="180")
        ttk.Entry(chunking_frame, textvariable=self.ai_chunking_timeout_var, width=20).grid(row=8, column=1, sticky="w", padx=(5, 0), pady=2)

        # AI分块回退选项
        self.ai_chunking_fallback_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(chunking_frame, text="AI分块失败时回退到规则分块",
                       variable=self.ai_chunking_fallback_var).grid(row=9, column=0, columnspan=2, sticky="w", pady=2)

        # 智能分块管理按钮
        chunking_button_frame = ttk.Frame(chunking_frame)
        chunking_button_frame.grid(row=10, column=0, columnspan=2, sticky="w", pady=10)

        ttk.Button(chunking_button_frame, text="查看失败块统计", command=self.show_failed_blocks_stats).pack(side="left", padx=(0, 10))
        ttk.Button(chunking_button_frame, text="管理失败块", command=self.show_failed_blocks_manager).pack(side="left", padx=(0, 10))
        ttk.Button(chunking_button_frame, text="分块预览", command=self.show_chunking_preview).pack(side="left")

        # 设置按钮
        settings_button_frame = ttk.Frame(self.settings_frame)
        settings_button_frame.pack(fill="x", padx=10, pady=10)

        ttk.Button(settings_button_frame, text="保存设置",
                  command=self.save_settings).pack(side="left", padx=(0, 10))
        ttk.Button(settings_button_frame, text="重置默认",
                  command=self.reset_settings).pack(side="left")

    def create_status_area(self):
        """创建状态栏和日志区域"""
        # 状态栏
        self.status_frame = ttk.Frame(self.root)
        self.status_frame.pack(fill="x", side="bottom")

        self.status_var = tk.StringVar(value="准备就绪")
        self.status_label = ttk.Label(self.status_frame, textvariable=self.status_var, relief="sunken")
        self.status_label.pack(side="left", fill="x", expand=True, padx=2, pady=2)

        # 进度条（隐藏状态）
        self.main_progress = ttk.Progressbar(self.status_frame, mode='indeterminate')
        # 不立即显示进度条

        # 日志区域（可折叠）
        self.log_frame = ttk.LabelFrame(self.root, text="日志", padding=5)
        # 默认不显示日志区域

        self.log_text = scrolledtext.ScrolledText(self.log_frame, height=6, wrap=tk.WORD)
        self.log_text.pack(fill="both", expand=True)

        # 日志控制按钮
        log_button_frame = ttk.Frame(self.log_frame)
        log_button_frame.pack(fill="x", pady=(5, 0))

        ttk.Button(log_button_frame, text="清空日志",
                  command=self.clear_log).pack(side="left")
        ttk.Button(log_button_frame, text="保存日志",
                  command=self.save_log).pack(side="left", padx=(5, 0))

        # 切换日志显示的按钮
        self.toggle_log_btn = ttk.Button(self.status_frame, text="显示日志",
                                       command=self.toggle_log_display)
        self.toggle_log_btn.pack(side="right", padx=2, pady=2)

        self.log_visible = False

    # ==================== 文件选择方法 ====================

    def browse_single_file(self):
        """浏览选择单个文档文件"""
        filetypes = [
            ("所有支持的文档", "*.pdf;*.docx;*.doc;*.md;*.txt"),
            ("PDF文件", "*.pdf"),
            ("Word文档", "*.docx;*.doc"),
            ("Markdown文件", "*.md"),
            ("文本文件", "*.txt"),
            ("所有文件", "*.*")
        ]

        filename = filedialog.askopenfilename(
            title="选择要处理的文档文件",
            filetypes=filetypes
        )

        if filename:
            self.selected_file_var.set(filename)
            self.log_message(f"选择文件: {filename}")

    def browse_directory(self):
        """浏览选择目录"""
        directory = filedialog.askdirectory(
            title="选择包含文档的目录"
        )

        if directory:
            self.selected_dir_var.set(directory)
            self.log_message(f"选择目录: {directory}")

    def browse_markdown_file(self):
        """浏览选择Markdown文件"""
        filename = filedialog.askopenfilename(
            title="选择Markdown文件",
            filetypes=[("Markdown文件", "*.md"), ("所有文件", "*.*")]
        )

        if filename:
            self.md_file_var.set(filename)
            self.log_message(f"选择Markdown文件: {filename}")

    # ==================== 处理方法 ====================

    def process_single_document(self):
        """处理单个文档"""
        if self.processing:
            messagebox.showwarning("处理中", "已有处理任务在进行中，请等待完成")
            return

        file_path = self.selected_file_var.get().strip()
        if not file_path:
            messagebox.showerror("错误", "请先选择要处理的文档文件")
            return

        if not os.path.exists(file_path):
            messagebox.showerror("错误", f"文件不存在: {file_path}")
            return

        # 清空结果显示
        self.single_result_text.delete(1.0, tk.END)

        # 启动处理线程
        self.start_processing("single_document", {
            'file_path': file_path,
            'to_markdown': self.process_to_markdown_var.get(),
            'generate_anki': self.generate_anki_var.get()
        })

    def process_directory(self):
        """批量处理目录"""
        if self.processing:
            messagebox.showwarning("处理中", "已有处理任务在进行中，请等待完成")
            return

        dir_path = self.selected_dir_var.get().strip()
        if not dir_path:
            messagebox.showerror("错误", "请先选择要处理的目录")
            return

        if not os.path.exists(dir_path):
            messagebox.showerror("错误", f"目录不存在: {dir_path}")
            return

        # 清空结果显示
        self.batch_result_text.delete(1.0, tk.END)
        self.batch_progress['value'] = 0
        self.batch_status_var.set("准备开始批量处理...")

        # 启动处理线程
        self.start_processing("batch_process", {
            'dir_path': dir_path,
            'recursive': self.recursive_var.get(),
            'skip_existing': self.skip_existing_var.get()
        })

    def generate_anki_cards(self):
        """生成Anki闪卡"""
        if self.processing:
            messagebox.showwarning("处理中", "已有处理任务在进行中，请等待完成")
            return

        project_name = self.anki_project_var.get().strip()
        if not project_name:
            messagebox.showerror("错误", "请输入项目名称")
            return

        if len(project_name) < 5:
            messagebox.showerror("错误", "项目名称至少需要5个字符以支持模糊匹配")
            return

        # 清空结果显示
        self.anki_result_text.delete(1.0, tk.END)

        # 启动处理线程
        self.start_processing("generate_anki", {
            'project_name': project_name,
            'interactive': self.interactive_mode_var.get()
        })

    def convert_markdown_to_anki(self):
        """直接转换Markdown为Anki"""
        if self.processing:
            messagebox.showwarning("处理中", "已有处理任务在进行中，请等待完成")
            return

        md_file = self.md_file_var.get().strip()
        if not md_file:
            messagebox.showerror("错误", "请先选择Markdown文件")
            return

        if not os.path.exists(md_file):
            messagebox.showerror("错误", f"文件不存在: {md_file}")
            return

        # 清空结果显示
        self.anki_result_text.delete(1.0, tk.END)

        # 启动处理线程
        self.start_processing("md_to_anki", {
            'md_file': md_file
        })

    def preprocess_markdown_file_only(self):
        """只预处理Markdown文件"""
        if self.processing:
            messagebox.showwarning("处理中", "已有处理任务在进行中，请等待完成")
            return

        md_file = self.md_file_var.get().strip()
        if not md_file:
            messagebox.showerror("错误", "请先选择Markdown文件")
            return

        if not os.path.exists(md_file):
            messagebox.showerror("错误", f"文件不存在: {md_file}")
            return

        # 清空结果显示
        self.md_anki_result_text.delete(1.0, tk.END)

        # 启动处理线程
        self.start_processing("preprocess_only", {
            'md_file': md_file
        })

    # ==================== 线程处理方法 ====================

    def start_processing(self, operation_type: str, params: Dict[str, Any]):
        """启动处理线程"""
        self.processing = True
        self.current_operation = operation_type

        # 显示进度条
        self.show_progress()

        # 禁用相关按钮
        self.disable_processing_buttons()

        # 启动工作线程
        thread = threading.Thread(
            target=self.processing_worker,
            args=(operation_type, params),
            daemon=True
        )
        thread.start()

    def processing_worker(self, operation_type: str, params: Dict[str, Any]):
        """处理工作线程"""
        try:
            if operation_type == "single_document":
                result = self.process_single_document_worker(params)
            elif operation_type == "batch_process":
                result = self.process_directory_worker(params)
            elif operation_type == "generate_anki":
                result = self.generate_anki_worker(params)
            elif operation_type == "md_to_anki":
                result = self.md_to_anki_worker(params)
            elif operation_type == "batch_generate_anki":
                result = self.batch_generate_anki_worker(params)
            elif operation_type == "preprocess_only":
                result = self.preprocess_only_worker(params)
            else:
                raise ValueError(f"未知的操作类型: {operation_type}")

            # 发送成功结果到主线程
            self.message_queue.put(("success", operation_type, result))

        except Exception as e:
            # 发送错误结果到主线程
            self.message_queue.put(("error", operation_type, str(e)))

    def process_single_document_worker(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """单文档处理工作函数"""
        file_path = params['file_path']
        to_markdown = params['to_markdown']
        generate_anki = params['generate_anki']

        self.message_queue.put(("status", "正在处理文档...", None))

        if to_markdown:
            # 处理文档为Markdown
            result = self.processor.process_document(file_path)

            if generate_anki:
                # 同时生成Anki
                self.message_queue.put(("status", "正在生成Anki闪卡...", None))
                anki_result = self.processor.generate_anki_for_project(
                    result['project_name'],
                    interactive=False  # GUI模式下使用非交互式
                )
                result['anki_result'] = anki_result

        return result

    def process_directory_worker(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """批量处理工作函数"""
        dir_path = params['dir_path']
        recursive = params['recursive']
        skip_existing = params['skip_existing']

        self.message_queue.put(("status", "扫描目录中的文档...", None))

        # 扫描文档文件
        supported_extensions = {'.pdf', '.docx', '.doc', '.md', '.txt'}
        files_to_process = []

        if recursive:
            for root, _, files in os.walk(dir_path):
                for file in files:
                    if Path(file).suffix.lower() in supported_extensions:
                        files_to_process.append(os.path.join(root, file))
        else:
            for file in os.listdir(dir_path):
                file_path = os.path.join(dir_path, file)
                if os.path.isfile(file_path) and Path(file).suffix.lower() in supported_extensions:
                    files_to_process.append(file_path)

        if not files_to_process:
            raise ValueError("在指定目录中未找到支持的文档文件")

        self.message_queue.put(("batch_total", len(files_to_process), None))

        results = []
        for i, file_path in enumerate(files_to_process):
            try:
                self.message_queue.put(("status", f"处理文件 {i+1}/{len(files_to_process)}: {os.path.basename(file_path)}", None))
                self.message_queue.put(("batch_progress", i, None))

                # 检查是否跳过已处理的文件
                if skip_existing:
                    # 这里可以添加检查逻辑
                    pass

                result = self.processor.process_document(file_path)
                results.append(result)

            except Exception as e:
                error_result = {
                    'original_file': file_path,
                    'error': str(e)
                }
                results.append(error_result)

        self.message_queue.put(("batch_progress", len(files_to_process), None))

        return {
            'total_files': len(files_to_process),
            'results': results,
            'success_count': len([r for r in results if 'error' not in r]),
            'error_count': len([r for r in results if 'error' in r])
        }

    def generate_anki_worker(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Anki生成工作函数"""
        project_name = params['project_name']
        # interactive = params['interactive']  # GUI模式下总是使用非交互式

        self.message_queue.put(("status", f"搜索匹配项目: {project_name}", None))

        # 在GUI模式下，我们需要处理交互式选择
        result = self.processor.generate_anki_for_project(
            project_name,
            interactive=False  # GUI模式下总是使用非交互式
        )

        return result

    def md_to_anki_worker(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Markdown转Anki工作函数"""
        md_file = params['md_file']

        self.message_queue.put(("status", f"处理Markdown文件: {os.path.basename(md_file)}", None))

        result = self.processor.process_markdown_to_anki(md_file)

        return result

    def batch_generate_anki_worker(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """批量生成Anki工作函数"""
        project_names = params['project_names']

        self.message_queue.put(("status", f"开始批量生成Anki，共 {len(project_names)} 个项目", None))
        self.message_queue.put(("batch_total", len(project_names), None))

        results = []
        success_count = 0
        error_count = 0

        for i, project_name in enumerate(project_names):
            try:
                self.message_queue.put(("status", f"正在处理项目 {i+1}/{len(project_names)}: {project_name}", None))
                self.message_queue.put(("batch_progress", i, None))

                # 生成Anki闪卡
                result = self.processor.generate_anki_for_project(
                    project_name,
                    interactive=False  # 批量模式下使用非交互式
                )

                results.append({
                    'project_name': project_name,
                    'status': 'success',
                    'result': result
                })
                success_count += 1

            except Exception as e:
                results.append({
                    'project_name': project_name,
                    'status': 'error',
                    'error': str(e)
                })
                error_count += 1
                self.message_queue.put(("status", f"项目 {project_name} 处理失败: {str(e)}", None))

        self.message_queue.put(("batch_progress", len(project_names), None))

        return {
            'total_projects': len(project_names),
            'success_count': success_count,
            'error_count': error_count,
            'results': results
        }

    def preprocess_only_worker(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """只预处理Markdown文件工作函数"""
        md_file = params['md_file']

        self.message_queue.put(("status", f"开始预处理Markdown文件: {os.path.basename(md_file)}", None))

        if not self.processor or not hasattr(self.processor, 'semantic_chunker'):
            raise ValueError("智能分块功能未初始化")

        # 读取Markdown内容
        with open(md_file, 'r', encoding='utf-8') as f:
            content = f.read()

        # 生成项目名称（基于文件名）
        project_name = Path(md_file).stem

        self.message_queue.put(("status", "正在进行AI语义分块...", None))

        # 执行语义分块预处理
        chunking_result = self.processor.semantic_chunker.preprocess_document(content, project_name)

        if not chunking_result.success:
            raise ValueError(f"语义分块失败: {chunking_result.error_message}")

        # 保存预处理结果
        preprocessed_file = Path(md_file).parent / f"{project_name}_preprocessed.md"

        self.message_queue.put(("status", "保存预处理结果...", None))

        if not self.processor.semantic_chunker.save_preprocessed_content(
            chunking_result.preprocessed_content, preprocessed_file):
            raise ValueError("保存预处理文件失败")

        return {
            'original_file': md_file,
            'preprocessed_file': str(preprocessed_file),
            'project_name': project_name,
            'chunk_count': chunking_result.chunk_count,
            'processing_time': chunking_result.processing_time,
            'original_length': chunking_result.original_length,
            'preprocessed_length': chunking_result.preprocessed_length
        }

    # ==================== UI更新方法 ====================

    def process_queue(self):
        """处理消息队列"""
        try:
            while True:
                message_type, data, extra = self.message_queue.get_nowait()

                if message_type == "status":
                    self.status_var.set(data)
                elif message_type == "batch_total":
                    self.batch_progress['maximum'] = data
                elif message_type == "batch_progress":
                    self.batch_progress['value'] = data
                    if data < self.batch_progress['maximum']:
                        self.batch_status_var.set(f"处理进度: {data}/{int(self.batch_progress['maximum'])}")
                    else:
                        self.batch_status_var.set("批量处理完成")
                elif message_type == "success":
                    self.handle_processing_success(data, extra)
                elif message_type == "error":
                    self.handle_processing_error(data, extra)

        except queue.Empty:
            pass

        # 继续处理队列
        self.root.after(100, self.process_queue)

    def handle_processing_success(self, operation_type: str, result: Dict[str, Any]):
        """处理成功结果"""
        self.processing = False
        self.hide_progress()
        self.enable_processing_buttons()
        self.status_var.set("处理完成")

        if operation_type == "single_document":
            self.display_single_document_result(result)
        elif operation_type == "batch_process":
            self.display_batch_process_result(result)
        elif operation_type == "generate_anki":
            self.display_anki_result(result, target="project")
        elif operation_type == "md_to_anki":
            self.display_anki_result(result, target="markdown")
        elif operation_type == "preprocess_only":
            self.display_preprocess_result(result)
        elif operation_type == "batch_generate_anki":
            self.display_batch_anki_result(result)

        # 刷新项目列表
        self.refresh_project_list()

    def handle_processing_error(self, operation_type: str, error_message: str):
        """处理错误结果"""
        self.processing = False
        self.hide_progress()
        self.enable_processing_buttons()
        self.status_var.set("处理失败")

        self.log_message(f"处理失败: {error_message}", level="ERROR")
        messagebox.showerror("处理失败", f"操作失败:\n{error_message}")

    def display_single_document_result(self, result: Dict[str, Any]):
        """显示单文档处理结果"""
        text = self.single_result_text
        text.delete(1.0, tk.END)

        text.insert(tk.END, "✅ 文档处理完成!\n\n")
        text.insert(tk.END, f"项目名称: {result['project_name']}\n")
        text.insert(tk.END, f"文档类型: {result['document_type'].upper()}\n")
        text.insert(tk.END, f"原始文件: {result['original_file']}\n")
        text.insert(tk.END, f"Markdown文件: {result['markdown_file']}\n")

        if 'image_count' in result:
            text.insert(tk.END, f"图片数量: {result['image_count']}\n")
        if 'total_pages' in result:
            text.insert(tk.END, f"总页数: {result['total_pages']}\n")
        if 'paragraph_count' in result:
            text.insert(tk.END, f"段落数: {result['paragraph_count']}\n")

        if 'anki_result' in result:
            anki_result = result['anki_result']
            text.insert(tk.END, f"\n🎴 Anki闪卡生成完成!\n")
            text.insert(tk.END, f"输出文件: {anki_result['output_file']}\n")
            text.insert(tk.END, f"总卡片数: {anki_result['total_cards']}\n")
            text.insert(tk.END, f"包含图片的卡片: {anki_result['questions_with_images']}\n")
            text.insert(tk.END, f"图片总数: {anki_result['total_images']}\n")

    def display_batch_process_result(self, result: Dict[str, Any]):
        """显示批量处理结果"""
        text = self.batch_result_text
        text.delete(1.0, tk.END)

        text.insert(tk.END, f"📁 批量处理完成!\n\n")
        text.insert(tk.END, f"总文件数: {result['total_files']}\n")
        text.insert(tk.END, f"成功处理: {result['success_count']}\n")
        text.insert(tk.END, f"处理失败: {result['error_count']}\n\n")

        text.insert(tk.END, "处理详情:\n")
        text.insert(tk.END, "-" * 50 + "\n")

        for item in result['results']:
            if 'error' in item:
                text.insert(tk.END, f"❌ {os.path.basename(item['original_file'])}: {item['error']}\n")
            else:
                text.insert(tk.END, f"✅ {item['project_name']}: {item['document_type'].upper()}")
                if 'image_count' in item:
                    text.insert(tk.END, f" ({item['image_count']}张图片)")
                text.insert(tk.END, "\n")

    def display_anki_result(self, result: Dict[str, Any], target: str = "markdown"):
        """显示Anki生成结果"""
        if target == "markdown":
            text = self.md_anki_result_text
        else:
            # 如果是项目管理中生成的，显示在单文档处理结果中
            text = self.single_result_text

        text.delete(1.0, tk.END)

        text.insert(tk.END, "🎴 Anki闪卡生成完成!\n\n")
        text.insert(tk.END, f"项目名称: {result['project_name']}\n")
        text.insert(tk.END, f"输入文件: {result['input_file']}\n")
        text.insert(tk.END, f"输出文件: {result['output_file']}\n")
        text.insert(tk.END, f"缓存目录: {result['cache_dir']}\n\n")
        text.insert(tk.END, f"总卡片数: {result['total_cards']}\n")
        text.insert(tk.END, f"包含图片的卡片: {result['questions_with_images']}\n")
        text.insert(tk.END, f"图片总数: {result['total_images']}\n")
        text.insert(tk.END, f"源数据块: {result['source_chunks']}\n")

    def display_preprocess_result(self, result: Dict[str, Any]):
        """显示预处理结果"""
        text = self.md_anki_result_text
        text.delete(1.0, tk.END)

        text.insert(tk.END, "🔄 Markdown预处理完成!\n\n")
        text.insert(tk.END, f"项目名称: {result['project_name']}\n")
        text.insert(tk.END, f"原始文件: {result['original_file']}\n")
        text.insert(tk.END, f"预处理文件: {result['preprocessed_file']}\n\n")
        text.insert(tk.END, f"语义块数量: {result['chunk_count']}\n")
        text.insert(tk.END, f"处理时间: {result['processing_time']:.2f}秒\n")
        text.insert(tk.END, f"原始长度: {result['original_length']:,} 字符\n")
        text.insert(tk.END, f"处理后长度: {result['preprocessed_length']:,} 字符\n\n")

        # 计算增长比例
        if result['original_length'] > 0:
            growth_ratio = (result['preprocessed_length'] - result['original_length']) / result['original_length']
            text.insert(tk.END, f"内容增长: {growth_ratio:.1%}\n")

        text.insert(tk.END, "\n✅ 预处理文件已保存，可以用于后续的Anki生成或其他处理。\n")
        text.insert(tk.END, "💡 提示: 启用智能分块模式后，系统将自动使用预处理文件进行语义分块。")

    def display_batch_anki_result(self, result: Dict[str, Any]):
        """显示批量Anki生成结果"""
        # 显示在单文档处理结果区域
        text = self.single_result_text
        text.delete(1.0, tk.END)

        text.insert(tk.END, "🎴 批量Anki闪卡生成完成!\n\n")
        text.insert(tk.END, f"总项目数: {result['total_projects']}\n")
        text.insert(tk.END, f"成功生成: {result['success_count']}\n")
        text.insert(tk.END, f"生成失败: {result['error_count']}\n\n")

        text.insert(tk.END, "生成详情:\n")
        text.insert(tk.END, "-" * 50 + "\n")

        for item in result['results']:
            if item['status'] == 'error':
                text.insert(tk.END, f"❌ {item['project_name']}: {item['error']}\n")
            else:
                anki_result = item['result']
                text.insert(tk.END, f"✅ {item['project_name']}: {anki_result['total_cards']}张卡片")
                if anki_result['questions_with_images'] > 0:
                    text.insert(tk.END, f" ({anki_result['questions_with_images']}张含图片)")
                text.insert(tk.END, "\n")

    # ==================== 项目管理方法 ====================

    def refresh_project_list(self):
        """刷新项目列表"""
        if not self.processor:
            return

        # 清空现有项目
        for item in self.project_tree.get_children():
            self.project_tree.delete(item)

        try:
            projects = self.processor.file_manager.list_projects()

            for project in projects:
                # 确定项目状态
                status_parts = []
                if project['has_images']:
                    status_parts.append(f"{project['image_count']}图片")

                # 使用新的缓存进度信息
                cache_info = project['cache_progress']
                if cache_info['cached'] > 0:
                    status_parts.append(f"缓存{cache_info['status']}")

                if project['has_anki']:
                    status_parts.append("已生成Anki")

                status = ", ".join(status_parts) if status_parts else "仅Markdown"

                # 插入项目到树形控件
                self.project_tree.insert("", "end",
                                       text=project['name'],
                                       values=(
                                           os.path.basename(project['markdown_file']),
                                           "Mixed",  # 类型暂时显示为Mixed
                                           project['image_count'],
                                           status
                                       ))

            self.log_message(f"刷新项目列表完成，共 {len(projects)} 个项目")

        except Exception as e:
            self.log_message(f"刷新项目列表失败: {e}", level="ERROR")

    def filter_projects(self, *args):
        """过滤项目列表"""
        search_term = self.project_search_var.get().lower()

        # 如果搜索框为空，显示所有项目
        if not search_term:
            self.refresh_project_list()
            return

        # 清空现有显示
        for item in self.project_tree.get_children():
            self.project_tree.delete(item)

        try:
            projects = self.processor.file_manager.list_projects()

            # 过滤匹配的项目
            filtered_projects = [
                p for p in projects
                if search_term in p['name'].lower() or
                   search_term in os.path.basename(p['markdown_file']).lower()
            ]

            for project in filtered_projects:
                status_parts = []
                if project['has_images']:
                    status_parts.append(f"{project['image_count']}图片")

                # 使用新的缓存进度信息
                cache_info = project['cache_progress']
                if cache_info['cached'] > 0:
                    status_parts.append(f"缓存{cache_info['status']}")

                if project['has_anki']:
                    status_parts.append("已生成Anki")

                status = ", ".join(status_parts) if status_parts else "仅Markdown"

                self.project_tree.insert("", "end",
                                       text=project['name'],
                                       values=(
                                           os.path.basename(project['markdown_file']),
                                           "Mixed",
                                           project['image_count'],
                                           status
                                       ))

        except Exception as e:
            self.log_message(f"过滤项目失败: {e}", level="ERROR")

    def search_projects_for_anki(self):
        """为Anki生成搜索项目"""
        search_term = self.anki_project_var.get().strip()
        if not search_term:
            messagebox.showwarning("警告", "请输入项目名称进行搜索")
            return

        try:
            projects = self.processor.file_manager.list_projects()

            # 模糊匹配项目
            matches = []
            for project in projects:
                if (search_term.lower() in project['name'].lower() or
                    project['name'].lower().startswith(search_term.lower())):
                    matches.append(project)

            if not matches:
                messagebox.showinfo("搜索结果", f"未找到匹配 '{search_term}' 的项目")
                return

            if len(matches) == 1:
                # 只有一个匹配项，直接设置
                self.anki_project_var.set(matches[0]['name'])
                messagebox.showinfo("搜索结果", f"找到匹配项目: {matches[0]['name']}")
            else:
                # 多个匹配项，显示选择对话框
                self.show_project_selection_dialog(matches)

        except Exception as e:
            self.log_message(f"搜索项目失败: {e}", level="ERROR")
            messagebox.showerror("错误", f"搜索项目失败: {e}")

    def show_project_selection_dialog(self, projects):
        """显示项目选择对话框"""
        dialog = tk.Toplevel(self.root)
        dialog.title("选择项目")
        dialog.geometry("600x400")
        dialog.transient(self.root)
        dialog.grab_set()

        # 项目列表
        ttk.Label(dialog, text="找到多个匹配的项目，请选择一个:").pack(pady=10)

        listbox_frame = ttk.Frame(dialog)
        listbox_frame.pack(fill="both", expand=True, padx=10, pady=5)

        listbox = tk.Listbox(listbox_frame)
        scrollbar = ttk.Scrollbar(listbox_frame, orient="vertical", command=listbox.yview)
        listbox.configure(yscrollcommand=scrollbar.set)

        for project in projects:
            status_info = f" ({project['image_count']}图片)" if project['has_images'] else ""
            listbox.insert(tk.END, f"{project['name']}{status_info}")

        listbox.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # 按钮
        button_frame = ttk.Frame(dialog)
        button_frame.pack(fill="x", padx=10, pady=10)

        def on_select():
            selection = listbox.curselection()
            if selection:
                selected_project = projects[selection[0]]
                self.anki_project_var.set(selected_project['name'])
                dialog.destroy()
            else:
                messagebox.showwarning("警告", "请选择一个项目")

        def on_cancel():
            dialog.destroy()

        ttk.Button(button_frame, text="选择", command=on_select).pack(side="right", padx=(5, 0))
        ttk.Button(button_frame, text="取消", command=on_cancel).pack(side="right")

        # 双击选择
        listbox.bind("<Double-Button-1>", lambda e: on_select())

        # 居中显示对话框
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (dialog.winfo_width() // 2)
        y = (dialog.winfo_screenheight() // 2) - (dialog.winfo_height() // 2)
        dialog.geometry(f"+{x}+{y}")

    def view_project_details(self):
        """查看项目详情"""
        selection = self.project_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择一个项目")
            return

        project_name = self.project_tree.item(selection[0])['text']

        try:
            projects = self.processor.file_manager.list_projects()
            project = next((p for p in projects if p['name'] == project_name), None)

            if not project:
                messagebox.showerror("错误", "项目不存在")
                return

            # 显示项目详情对话框
            self.show_project_details_dialog(project)

        except Exception as e:
            self.log_message(f"查看项目详情失败: {e}", level="ERROR")
            messagebox.showerror("错误", f"查看项目详情失败: {e}")

    def show_project_details_dialog(self, project):
        """显示项目详情对话框"""
        dialog = tk.Toplevel(self.root)
        dialog.title(f"项目详情 - {project['name']}")
        dialog.geometry("500x400")
        dialog.transient(self.root)

        # 创建详情文本
        text_frame = ttk.Frame(dialog)
        text_frame.pack(fill="both", expand=True, padx=10, pady=10)

        details_text = scrolledtext.ScrolledText(text_frame, wrap=tk.WORD)
        details_text.pack(fill="both", expand=True)

        # 填充项目详情
        details_text.insert(tk.END, f"项目名称: {project['name']}\n")
        details_text.insert(tk.END, f"项目路径: {project['path']}\n")
        details_text.insert(tk.END, f"Markdown文件: {project['markdown_file']}\n")
        details_text.insert(tk.END, f"包含图片: {'是' if project['has_images'] else '否'}\n")
        details_text.insert(tk.END, f"图片数量: {project['image_count']}\n")

        # 使用新的缓存进度信息
        cache_info = project['cache_progress']
        details_text.insert(tk.END, f"缓存进度: {cache_info['status']}\n")
        details_text.insert(tk.END, f"缓存详情: {cache_info['cached']}/{cache_info['total']} ({cache_info['percentage']:.1f}%)\n")

        details_text.insert(tk.END, f"已生成Anki: {'是' if project['has_anki'] else '否'}\n")

        details_text.config(state="disabled")

        # 关闭按钮
        ttk.Button(dialog, text="关闭", command=dialog.destroy).pack(pady=10)

    def open_project_folder(self):
        """打开项目文件夹"""
        selection = self.project_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择一个项目")
            return

        project_name = self.project_tree.item(selection[0])['text']

        try:
            projects = self.processor.file_manager.list_projects()
            project = next((p for p in projects if p['name'] == project_name), None)

            if not project:
                messagebox.showerror("错误", "项目不存在")
                return

            # 打开项目文件夹
            import subprocess
            import platform

            folder_path = project['path']

            if platform.system() == "Windows":
                subprocess.run(["explorer", folder_path])
            elif platform.system() == "Darwin":  # macOS
                subprocess.run(["open", folder_path])
            else:  # Linux
                subprocess.run(["xdg-open", folder_path])

        except Exception as e:
            self.log_message(f"打开项目文件夹失败: {e}", level="ERROR")
            messagebox.showerror("错误", f"打开项目文件夹失败: {e}")

    def delete_project(self):
        """删除项目"""
        selection = self.project_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择一个项目")
            return

        project_name = self.project_tree.item(selection[0])['text']

        # 确认删除
        result = messagebox.askyesno(
            "确认删除",
            f"确定要删除项目 '{project_name}' 吗？\n\n这将删除所有相关文件（Markdown、图片、缓存），但会保留Anki文件。"
        )

        if result:
            try:
                success = self.processor.file_manager.cleanup_project(project_name, keep_anki=True)
                if success:
                    messagebox.showinfo("删除成功", f"项目 '{project_name}' 已删除")
                    self.refresh_project_list()
                else:
                    messagebox.showerror("删除失败", f"删除项目 '{project_name}' 失败")

            except Exception as e:
                self.log_message(f"删除项目失败: {e}", level="ERROR")
                messagebox.showerror("错误", f"删除项目失败: {e}")

    # ==================== UI控制方法 ====================

    def show_progress(self):
        """显示进度条"""
        self.main_progress.pack(side="right", padx=(5, 2), pady=2)
        self.main_progress.start()

    def hide_progress(self):
        """隐藏进度条"""
        self.main_progress.stop()
        self.main_progress.pack_forget()

    def disable_processing_buttons(self):
        """禁用处理按钮"""
        self.process_single_btn.config(state="disabled")
        self.process_batch_btn.config(state="disabled")
        self.md_to_anki_btn.config(state="disabled")
        if hasattr(self, 'preprocess_only_btn'):
            self.preprocess_only_btn.config(state="disabled")

    def enable_processing_buttons(self):
        """启用处理按钮"""
        self.process_single_btn.config(state="normal")
        self.process_batch_btn.config(state="normal")
        self.md_to_anki_btn.config(state="normal")
        if hasattr(self, 'preprocess_only_btn'):
            self.preprocess_only_btn.config(state="normal")

    def toggle_log_display(self):
        """切换日志显示"""
        if self.log_visible:
            self.log_frame.pack_forget()
            self.toggle_log_btn.config(text="显示日志")
            self.log_visible = False
        else:
            self.log_frame.pack(fill="both", expand=True, padx=5, pady=(0, 5))
            self.toggle_log_btn.config(text="隐藏日志")
            self.log_visible = True

    def log_message(self, message: str, level: str = "INFO"):
        """记录日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {level}: {message}\n"

        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)

        # 限制日志长度
        lines = int(self.log_text.index('end-1c').split('.')[0])
        if lines > 1000:
            self.log_text.delete(1.0, "100.0")

    def clear_log(self):
        """清空日志"""
        self.log_text.delete(1.0, tk.END)
        self.log_message("日志已清空")

    def save_log(self):
        """保存日志"""
        filename = filedialog.asksaveasfilename(
            title="保存日志文件",
            defaultextension=".log",
            filetypes=[("日志文件", "*.log"), ("文本文件", "*.txt"), ("所有文件", "*.*")]
        )

        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(self.log_text.get(1.0, tk.END))
                messagebox.showinfo("保存成功", f"日志已保存到: {filename}")
            except Exception as e:
                messagebox.showerror("保存失败", f"保存日志失败: {e}")

    # ==================== 设置方法 ====================

    def refresh_prompt_templates(self):
        """刷新提示词模板列表"""
        try:
            # 检查必要的组件是否已初始化
            if not hasattr(self, 'prompt_template_combo') or not hasattr(self, 'prompt_info_var'):
                # 如果组件还没初始化，延迟执行
                self.root.after(100, self.refresh_prompt_templates)
                return

            if hasattr(self, 'processor') and self.processor and self.processor.ai_service and hasattr(self.processor.ai_service, 'get_prompt_templates'):
                templates = self.processor.ai_service.get_prompt_templates()
                template_names = [template['name'] for template in templates]
                self.prompt_template_combo['values'] = template_names

                # 设置当前选择的模板
                current_template = self.processor.ai_service.get_current_template_info()
                if current_template:
                    self.prompt_template_var.set(current_template['name'])
                    self.update_prompt_info(current_template)
                elif template_names:
                    self.prompt_template_var.set(template_names[0])
                    # 获取第一个模板的信息
                    first_template = next((t for t in templates if t['name'] == template_names[0]), None)
                    if first_template:
                        self.update_prompt_info(first_template)
            else:
                self.prompt_template_combo['values'] = []
                self.prompt_info_var.set("AI服务未初始化")
        except Exception as e:
            # 安全的日志记录
            if hasattr(self, 'log_message'):
                self.log_message(f"刷新提示词模板失败: {e}", level="ERROR")
            else:
                print(f"刷新提示词模板失败: {e}")

            if hasattr(self, 'prompt_template_combo'):
                self.prompt_template_combo['values'] = []
            if hasattr(self, 'prompt_info_var'):
                self.prompt_info_var.set("加载模板失败")

    def update_prompt_info(self, template_info):
        """更新提示词模板信息显示"""
        info_text = f"类型: {template_info.get('question_type', 'N/A')}, 格式: {template_info.get('answer_format', 'N/A')}"
        self.prompt_info_var.set(info_text)

    def on_prompt_template_selected(self, event=None):
        """提示词模板选择事件处理"""
        try:
            selected_template = self.prompt_template_var.get()
            if selected_template and self.processor and self.processor.ai_service:
                # 设置选择的模板
                success = self.processor.ai_service.set_prompt_template(selected_template)
                if success:
                    self.log_message(f"已切换到提示词模板: {selected_template}")
                    # 更新模板信息显示
                    current_template = self.processor.ai_service.get_current_template_info()
                    if current_template:
                        self.update_prompt_info(current_template)
                    # 保存选择的模板
                    self.selected_prompt_template = selected_template
                else:
                    self.log_message(f"切换提示词模板失败: {selected_template}", level="ERROR")
                    messagebox.showerror("错误", f"切换提示词模板失败: {selected_template}")
        except Exception as e:
            self.log_message(f"处理提示词模板选择失败: {e}", level="ERROR")

    def on_smart_chunking_changed(self):
        """智能分块设置变化处理"""
        try:
            enabled = self.enable_smart_chunking_var.get()
            self.log_message(f"智能分块设置: {'启用' if enabled else '禁用'}")

            # 这里可以添加实时配置更新逻辑
            # 暂时只记录日志，实际保存在save_settings中处理

        except Exception as e:
            self.log_message(f"处理智能分块设置变化失败: {e}", level="ERROR")

    def on_ai_chunking_changed(self):
        """AI分块设置变化处理"""
        try:
            enabled = self.enable_ai_chunking_var.get()
            self.log_message(f"AI分块设置: {'启用' if enabled else '禁用'}")

            # 这里可以添加实时配置更新逻辑
            # 暂时只记录日志，实际保存在save_settings中处理

        except Exception as e:
            self.log_message(f"处理AI分块设置变化失败: {e}", level="ERROR")

    def save_settings(self):
        """保存设置"""
        try:
            config_path = "config.ini"

            # 读取现有配置
            config = configparser.ConfigParser()
            if os.path.exists(config_path):
                config.read(config_path, encoding='utf-8')

            # 确保有DEFAULT section
            if 'DEFAULT' not in config:
                config.add_section('DEFAULT')

            # 保存处理设置
            config.set('DEFAULT', 'PDF_TOP_MARGIN', self.pdf_top_margin_var.get())
            config.set('DEFAULT', 'PDF_BOTTOM_MARGIN', self.pdf_bottom_margin_var.get())
            config.set('DEFAULT', 'CHUNK_SIZE', self.chunk_size_var.get())
            config.set('DEFAULT', 'CHUNK_STRIDE', self.chunk_stride_var.get())
            config.set('DEFAULT', 'LINES_TO_SKIP', self.lines_to_skip_var.get())

            # 保存智能分块设置
            config.set('DEFAULT', 'ENABLE_SMART_CHUNKING', 'true' if self.enable_smart_chunking_var.get() else 'false')
            config.set('DEFAULT', 'CONTENT_VALIDATION_THRESHOLD', self.validation_threshold_var.get())
            config.set('DEFAULT', 'MAX_CONTENT_RETRIES', self.max_retries_var.get())

            # 保存AI分块设置
            config.set('DEFAULT', 'ENABLE_AI_CHUNKING', 'true' if self.enable_ai_chunking_var.get() else 'false')
            config.set('DEFAULT', 'AI_CHUNKING_TARGET_SIZE', self.ai_chunking_target_size_var.get())
            config.set('DEFAULT', 'AI_CHUNKING_MAX_CONTENT_SIZE', self.ai_chunking_max_content_var.get())
            config.set('DEFAULT', 'AI_CHUNKING_TIMEOUT', self.ai_chunking_timeout_var.get())
            config.set('DEFAULT', 'AI_CHUNKING_FALLBACK_TO_RULES', 'true' if self.ai_chunking_fallback_var.get() else 'false')

            # 写入配置文件
            with open(config_path, 'w', encoding='utf-8') as f:
                config.write(f)

            self.log_message("设置已保存到config.ini")
            messagebox.showinfo("成功", "设置已保存")

            # 重新加载配置
            self.reload_config()

        except Exception as e:
            self.log_message(f"保存设置失败: {e}", level="ERROR")
            messagebox.showerror("错误", f"保存设置失败: {e}")

    def reset_settings(self):
        """重置设置"""
        # 重置为默认值
        self.pdf_top_margin_var.set("50.0")
        self.pdf_bottom_margin_var.set("50.0")
        self.chunk_size_var.set("500")
        self.chunk_stride_var.set("450")
        self.lines_to_skip_var.set("13")

        # 重置提示词模板选择
        self.refresh_prompt_templates()

        # 重置智能分块设置
        if hasattr(self, 'enable_smart_chunking_var'):
            self.enable_smart_chunking_var.set(False)
        if hasattr(self, 'validation_threshold_var'):
            self.validation_threshold_var.set("0.8")
        if hasattr(self, 'max_retries_var'):
            self.max_retries_var.set("2")

        # 重置AI分块设置
        if hasattr(self, 'enable_ai_chunking_var'):
            self.enable_ai_chunking_var.set(True)
        if hasattr(self, 'ai_chunking_target_size_var'):
            self.ai_chunking_target_size_var.set("12000")
        if hasattr(self, 'ai_chunking_max_content_var'):
            self.ai_chunking_max_content_var.set("50000")
        if hasattr(self, 'ai_chunking_timeout_var'):
            self.ai_chunking_timeout_var.set("180")
        if hasattr(self, 'ai_chunking_fallback_var'):
            self.ai_chunking_fallback_var.set(True)

        messagebox.showinfo("设置", "设置已重置为默认值")

    def show_channel_status(self):
        """显示AI通道状态"""
        try:
            if self.processor.ai_service and hasattr(self.processor.ai_service, 'get_stats'):
                stats = self.processor.ai_service.get_stats()

                # 创建状态显示窗口
                status_window = tk.Toplevel(self.root)
                status_window.title("AI通道状态")
                status_window.geometry("600x400")
                status_window.transient(self.root)
                status_window.grab_set()

                # 状态文本区域
                status_text = scrolledtext.ScrolledText(status_window, wrap=tk.WORD)
                status_text.pack(fill="both", expand=True, padx=10, pady=10)

                # 显示状态信息
                status_text.insert(tk.END, "🔗 AI通道状态\n")
                status_text.insert(tk.END, "=" * 50 + "\n\n")
                status_text.insert(tk.END, f"总通道数: {stats['total_channels']}\n")
                status_text.insert(tk.END, f"活跃通道数: {stats['active_channels']}\n\n")
                status_text.insert(tk.END, "通道详情:\n")
                status_text.insert(tk.END, "-" * 30 + "\n")

                for name, channel_stats in stats['channels'].items():
                    status_icon = "✅" if channel_stats['status'] == 'active' else "❌"
                    status_text.insert(tk.END, f"\n{status_icon} {name}:\n")
                    status_text.insert(tk.END, f"  状态: {channel_stats['status']}\n")
                    status_text.insert(tk.END, f"  总请求数: {channel_stats['total_requests']}\n")
                    status_text.insert(tk.END, f"  成功率: {channel_stats['success_rate']}%\n")
                    status_text.insert(tk.END, f"  平均响应时间: {channel_stats['avg_response_time']}s\n")
                    if channel_stats['error_message']:
                        status_text.insert(tk.END, f"  错误信息: {channel_stats['error_message']}\n")

                status_text.config(state='disabled')

                # 关闭按钮
                ttk.Button(status_window, text="关闭",
                          command=status_window.destroy).pack(pady=10)

            else:
                messagebox.showinfo("信息", "AI服务未初始化或不支持多通道功能")

        except Exception as e:
            messagebox.showerror("错误", f"获取通道状态失败: {e}")

    def reload_config(self):
        """重新加载配置"""
        try:
            # 重新初始化处理器
            prompt_template = getattr(self, 'selected_prompt_template', None)
            self.processor = DocumentProcessor(prompt_template=prompt_template)

            # 重新加载设置
            self.load_settings_from_config()

            # 刷新提示词模板列表
            self.refresh_prompt_templates()

            messagebox.showinfo("成功", "配置已重新加载")
            self.log_message("配置已重新加载")

        except Exception as e:
            messagebox.showerror("错误", f"重新加载配置失败: {e}")
            self.log_message(f"重新加载配置失败: {e}", level="ERROR")

    def show_channel_test(self):
        """显示AI通道测试窗口"""
        try:
            if not self.processor.ai_service or not hasattr(self.processor.ai_service, 'channels'):
                messagebox.showinfo("信息", "AI服务未初始化或不支持多通道功能")
                return

            # 创建测试窗口
            test_window = tk.Toplevel(self.root)
            test_window.title("AI通道测试")
            test_window.geometry("700x500")
            test_window.transient(self.root)
            test_window.grab_set()

            # 通道列表框架
            list_frame = ttk.LabelFrame(test_window, text="选择要测试的通道", padding=10)
            list_frame.pack(fill="both", expand=True, padx=10, pady=5)

            # 创建通道列表
            columns = ('name', 'status', 'provider', 'model', 'streaming')
            channel_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=8)

            # 设置列标题
            channel_tree.heading('name', text='通道名称')
            channel_tree.heading('status', text='状态')
            channel_tree.heading('provider', text='提供商')
            channel_tree.heading('model', text='模型')
            channel_tree.heading('streaming', text='流式')

            # 设置列宽
            channel_tree.column('name', width=120)
            channel_tree.column('status', width=80)
            channel_tree.column('provider', width=100)
            channel_tree.column('model', width=200)
            channel_tree.column('streaming', width=60)

            # 添加滚动条
            scrollbar = ttk.Scrollbar(list_frame, orient="vertical", command=channel_tree.yview)
            channel_tree.configure(yscrollcommand=scrollbar.set)

            channel_tree.pack(side="left", fill="both", expand=True)
            scrollbar.pack(side="right", fill="y")

            # 填充通道数据
            for channel in self.processor.ai_service.channels:
                status = "启用" if channel.enabled else "禁用"
                streaming = "是" if channel.use_streaming else "否"
                channel_tree.insert('', 'end', values=(
                    channel.name,
                    status,
                    channel.api_provider,
                    channel.model_name,
                    streaming
                ))

            # 测试结果显示区域
            result_frame = ttk.LabelFrame(test_window, text="测试结果", padding=10)
            result_frame.pack(fill="x", padx=10, pady=5)

            result_text = scrolledtext.ScrolledText(result_frame, height=8, wrap=tk.WORD)
            result_text.pack(fill="both", expand=True)

            # 按钮框架
            button_frame = ttk.Frame(test_window)
            button_frame.pack(fill="x", padx=10, pady=10)

            def test_selected_channel():
                """测试选中的通道"""
                selection = channel_tree.selection()
                if not selection:
                    messagebox.showwarning("警告", "请先选择要测试的通道")
                    return

                channel_name = channel_tree.item(selection[0])['values'][0]

                result_text.delete(1.0, tk.END)
                result_text.insert(tk.END, f"正在测试通道: {channel_name}...\n")
                result_text.update()

                # 在后台线程中执行测试
                def run_test():
                    try:
                        test_result = self.processor.ai_service.test_channel(channel_name)

                        # 在主线程中更新UI
                        def update_ui():
                            result_text.delete(1.0, tk.END)
                            result_text.insert(tk.END, f"通道测试结果: {channel_name}\n")
                            result_text.insert(tk.END, "=" * 40 + "\n\n")

                            if test_result['success']:
                                result_text.insert(tk.END, "✅ 测试成功!\n")
                                result_text.insert(tk.END, f"响应时间: {test_result['response_time']}秒\n")
                            else:
                                result_text.insert(tk.END, "❌ 测试失败!\n")
                                result_text.insert(tk.END, f"错误信息: {test_result['error']}\n")
                                result_text.insert(tk.END, f"响应时间: {test_result['response_time']}秒\n")

                        test_window.after(0, update_ui)

                    except Exception as e:
                        def show_error():
                            result_text.delete(1.0, tk.END)
                            result_text.insert(tk.END, f"测试异常: {str(e)}\n")

                        test_window.after(0, show_error)

                import threading
                threading.Thread(target=run_test, daemon=True).start()

            def test_all_channels():
                """测试所有通道"""
                result_text.delete(1.0, tk.END)
                result_text.insert(tk.END, "正在测试所有通道...\n")
                result_text.update()

                # 在后台线程中执行测试
                def run_test():
                    try:
                        test_results = self.processor.ai_service.test_all_channels()

                        # 在主线程中更新UI
                        def update_ui():
                            result_text.delete(1.0, tk.END)
                            result_text.insert(tk.END, "所有通道测试结果\n")
                            result_text.insert(tk.END, "=" * 40 + "\n\n")

                            for channel_name, result in test_results.items():
                                if result['success']:
                                    result_text.insert(tk.END, f"✅ {channel_name}: 测试成功 ({result['response_time']}s)\n")
                                else:
                                    result_text.insert(tk.END, f"❌ {channel_name}: {result['error']} ({result['response_time']}s)\n")

                            result_text.insert(tk.END, "\n测试完成!")

                        test_window.after(0, update_ui)

                    except Exception as e:
                        def show_error():
                            result_text.delete(1.0, tk.END)
                            result_text.insert(tk.END, f"测试异常: {str(e)}\n")

                        test_window.after(0, show_error)

                import threading
                threading.Thread(target=run_test, daemon=True).start()

            # 添加按钮
            ttk.Button(button_frame, text="测试选中通道", command=test_selected_channel).pack(side="left")
            ttk.Button(button_frame, text="测试所有通道", command=test_all_channels).pack(side="left", padx=(10, 0))
            ttk.Button(button_frame, text="关闭", command=test_window.destroy).pack(side="right")

        except Exception as e:
            messagebox.showerror("错误", f"打开通道测试窗口失败: {e}")

    def show_prompt_manager(self):
        """显示提示词管理窗口"""
        try:
            if not self.processor.ai_service or not hasattr(self.processor.ai_service, 'prompt_manager'):
                messagebox.showinfo("信息", "AI服务未初始化或不支持提示词管理功能")
                return

            # 创建提示词管理窗口
            prompt_window = tk.Toplevel(self.root)
            prompt_window.title("提示词管理")
            prompt_window.geometry("800x600")
            prompt_window.transient(self.root)
            prompt_window.grab_set()

            # 创建主框架
            main_frame = ttk.Frame(prompt_window)
            main_frame.pack(fill="both", expand=True, padx=10, pady=10)

            # 左侧：模板列表
            left_frame = ttk.LabelFrame(main_frame, text="提示词模板", padding=10)
            left_frame.pack(side="left", fill="both", expand=True, padx=(0, 5))

            # 模板列表
            columns = ('name', 'description', 'type', 'format', 'current')
            template_tree = ttk.Treeview(left_frame, columns=columns, show='headings', height=12)

            # 设置列标题
            template_tree.heading('name', text='名称')
            template_tree.heading('description', text='描述')
            template_tree.heading('type', text='题目类型')
            template_tree.heading('format', text='答案格式')
            template_tree.heading('current', text='当前')

            # 设置列宽
            template_tree.column('name', width=120)
            template_tree.column('description', width=200)
            template_tree.column('type', width=100)
            template_tree.column('format', width=100)
            template_tree.column('current', width=60)

            # 添加滚动条
            template_scrollbar = ttk.Scrollbar(left_frame, orient="vertical", command=template_tree.yview)
            template_tree.configure(yscrollcommand=template_scrollbar.set)

            template_tree.pack(side="left", fill="both", expand=True)
            template_scrollbar.pack(side="right", fill="y")

            # 右侧：模板详情和操作
            right_frame = ttk.LabelFrame(main_frame, text="模板详情", padding=10)
            right_frame.pack(side="right", fill="both", expand=True, padx=(5, 0))

            # 当前模板信息
            current_info_frame = ttk.LabelFrame(right_frame, text="当前使用的模板", padding=10)
            current_info_frame.pack(fill="x", pady=(0, 10))

            current_info_text = tk.Text(current_info_frame, height=4, wrap=tk.WORD, state='disabled')
            current_info_text.pack(fill="x")

            # 操作按钮
            button_frame = ttk.Frame(right_frame)
            button_frame.pack(fill="x", pady=10)

            def refresh_templates():
                """刷新模板列表"""
                # 清空现有项目
                for item in template_tree.get_children():
                    template_tree.delete(item)

                # 获取模板列表
                templates = self.processor.ai_service.get_prompt_templates()

                for template in templates:
                    current_mark = "✓" if template['is_current'] else ""
                    template_tree.insert('', 'end', values=(
                        template['name'],
                        template['description'],
                        template['question_type'],
                        template['answer_format'],
                        current_mark
                    ))

                # 更新当前模板信息
                current_template = self.processor.ai_service.get_current_template_info()
                if current_template:
                    info_text = f"名称: {current_template['name']}\n"
                    info_text += f"描述: {current_template['description']}\n"
                    info_text += f"题目类型: {current_template['question_type']}\n"
                    info_text += f"答案格式: {current_template['answer_format']}"

                    current_info_text.config(state='normal')
                    current_info_text.delete(1.0, tk.END)
                    current_info_text.insert(1.0, info_text)
                    current_info_text.config(state='disabled')

            def set_current_template():
                """设置当前模板"""
                selection = template_tree.selection()
                if not selection:
                    messagebox.showwarning("警告", "请先选择一个模板")
                    return

                template_name = template_tree.item(selection[0])['values'][0]

                try:
                    success = self.processor.ai_service.set_prompt_template(template_name)
                    if success:
                        messagebox.showinfo("成功", f"已切换到模板: {template_name}")
                        refresh_templates()
                        self.log_message(f"已切换到提示词模板: {template_name}")
                    else:
                        messagebox.showerror("错误", "切换模板失败")
                except Exception as e:
                    messagebox.showerror("错误", f"切换模板失败: {e}")

            # 添加按钮
            ttk.Button(button_frame, text="设为当前模板", command=set_current_template).pack(side="left")
            ttk.Button(button_frame, text="刷新列表", command=refresh_templates).pack(side="left", padx=(10, 0))
            ttk.Button(button_frame, text="关闭", command=prompt_window.destroy).pack(side="right")

            # 初始加载模板列表
            refresh_templates()

        except Exception as e:
            messagebox.showerror("错误", f"打开提示词管理窗口失败: {e}")

    def show_channel_test(self):
        """显示AI通道测试窗口"""
        try:
            if not self.processor.ai_service or not hasattr(self.processor.ai_service, 'channels'):
                messagebox.showinfo("信息", "AI服务未初始化或不支持多通道功能")
                return

            # 创建测试窗口
            test_window = tk.Toplevel(self.root)
            test_window.title("AI通道测试")
            test_window.geometry("700x500")
            test_window.transient(self.root)
            test_window.grab_set()

            # 通道列表框架
            list_frame = ttk.LabelFrame(test_window, text="选择要测试的通道", padding=10)
            list_frame.pack(fill="both", expand=True, padx=10, pady=5)

            # 创建通道列表
            columns = ('name', 'status', 'provider', 'model', 'streaming')
            channel_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=8)

            # 设置列标题
            channel_tree.heading('name', text='通道名称')
            channel_tree.heading('status', text='状态')
            channel_tree.heading('provider', text='提供商')
            channel_tree.heading('model', text='模型')
            channel_tree.heading('streaming', text='流式')

            # 设置列宽
            channel_tree.column('name', width=120)
            channel_tree.column('status', width=80)
            channel_tree.column('provider', width=100)
            channel_tree.column('model', width=200)
            channel_tree.column('streaming', width=60)

            # 添加滚动条
            scrollbar = ttk.Scrollbar(list_frame, orient="vertical", command=channel_tree.yview)
            channel_tree.configure(yscrollcommand=scrollbar.set)

            channel_tree.pack(side="left", fill="both", expand=True)
            scrollbar.pack(side="right", fill="y")

            # 填充通道数据
            for channel in self.processor.ai_service.channels:
                status = "启用" if channel.enabled else "禁用"
                streaming = "是" if channel.use_streaming else "否"
                channel_tree.insert('', 'end', values=(
                    channel.name,
                    status,
                    channel.api_provider,
                    channel.model_name,
                    streaming
                ))

            # 测试结果显示区域
            result_frame = ttk.LabelFrame(test_window, text="测试结果", padding=10)
            result_frame.pack(fill="x", padx=10, pady=5)

            result_text = scrolledtext.ScrolledText(result_frame, height=8, wrap=tk.WORD)
            result_text.pack(fill="both", expand=True)

            # 按钮框架
            button_frame = ttk.Frame(test_window)
            button_frame.pack(fill="x", padx=10, pady=10)

            def test_selected_channel():
                """测试选中的通道"""
                selection = channel_tree.selection()
                if not selection:
                    messagebox.showwarning("警告", "请先选择要测试的通道")
                    return

                channel_name = channel_tree.item(selection[0])['values'][0]

                result_text.delete(1.0, tk.END)
                result_text.insert(tk.END, f"正在测试通道: {channel_name}...\n")
                result_text.update()

                # 在后台线程中执行测试
                def run_test():
                    try:
                        test_result = self.processor.ai_service.test_channel(channel_name)

                        # 在主线程中更新UI
                        def update_ui():
                            result_text.delete(1.0, tk.END)
                            result_text.insert(tk.END, f"通道测试结果: {channel_name}\n")
                            result_text.insert(tk.END, "=" * 40 + "\n\n")

                            if test_result['success']:
                                result_text.insert(tk.END, "✅ 测试成功!\n")
                                result_text.insert(tk.END, f"响应时间: {test_result['response_time']}秒\n")
                            else:
                                result_text.insert(tk.END, "❌ 测试失败!\n")
                                result_text.insert(tk.END, f"错误信息: {test_result['error']}\n")
                                result_text.insert(tk.END, f"响应时间: {test_result['response_time']}秒\n")

                        test_window.after(0, update_ui)

                    except Exception as e:
                        def show_error():
                            result_text.delete(1.0, tk.END)
                            result_text.insert(tk.END, f"测试异常: {str(e)}\n")

                        test_window.after(0, show_error)

                import threading
                threading.Thread(target=run_test, daemon=True).start()

            def test_all_channels():
                """测试所有通道"""
                result_text.delete(1.0, tk.END)
                result_text.insert(tk.END, "正在测试所有通道...\n")
                result_text.update()

                # 在后台线程中执行测试
                def run_test():
                    try:
                        test_results = self.processor.ai_service.test_all_channels()

                        # 在主线程中更新UI
                        def update_ui():
                            result_text.delete(1.0, tk.END)
                            result_text.insert(tk.END, "所有通道测试结果\n")
                            result_text.insert(tk.END, "=" * 40 + "\n\n")

                            for channel_name, result in test_results.items():
                                if result['success']:
                                    result_text.insert(tk.END, f"✅ {channel_name}: 测试成功 ({result['response_time']}s)\n")
                                else:
                                    result_text.insert(tk.END, f"❌ {channel_name}: {result['error']} ({result['response_time']}s)\n")

                            result_text.insert(tk.END, "\n测试完成!")

                        test_window.after(0, update_ui)

                    except Exception as e:
                        def show_error():
                            result_text.delete(1.0, tk.END)
                            result_text.insert(tk.END, f"测试异常: {str(e)}\n")

                        test_window.after(0, show_error)

                import threading
                threading.Thread(target=run_test, daemon=True).start()

            # 添加按钮
            ttk.Button(button_frame, text="测试选中通道", command=test_selected_channel).pack(side="left")
            ttk.Button(button_frame, text="测试所有通道", command=test_all_channels).pack(side="left", padx=(10, 0))
            ttk.Button(button_frame, text="关闭", command=test_window.destroy).pack(side="right")

        except Exception as e:
            messagebox.showerror("错误", f"打开通道测试窗口失败: {e}")

    # ==================== 菜单方法 ====================

    def clear_cache(self):
        """清理缓存"""
        result = messagebox.askyesno("确认清理", "确定要清理所有缓存文件吗？")
        if result:
            try:
                cache_dir = Path("documents/cache")
                if cache_dir.exists():
                    import shutil
                    shutil.rmtree(cache_dir)
                    cache_dir.mkdir(exist_ok=True)
                messagebox.showinfo("清理完成", "缓存文件已清理")
                self.log_message("缓存文件已清理")
            except Exception as e:
                messagebox.showerror("清理失败", f"清理缓存失败: {e}")

    def export_project_list(self):
        """导出项目列表"""
        filename = filedialog.asksaveasfilename(
            title="导出项目列表",
            defaultextension=".csv",
            filetypes=[("CSV文件", "*.csv"), ("文本文件", "*.txt"), ("所有文件", "*.*")]
        )

        if filename:
            try:
                projects = self.processor.file_manager.list_projects()

                with open(filename, 'w', encoding='utf-8', newline='') as f:
                    import csv
                    writer = csv.writer(f)
                    writer.writerow(["项目名称", "Markdown文件", "图片数量", "缓存进度", "已生成Anki"])

                    for project in projects:
                        cache_info = project['cache_progress']
                        writer.writerow([
                            project['name'],
                            project['markdown_file'],
                            project['image_count'],
                            cache_info['status'],
                            "是" if project['has_anki'] else "否"
                        ])

                messagebox.showinfo("导出成功", f"项目列表已导出到: {filename}")

            except Exception as e:
                messagebox.showerror("导出失败", f"导出项目列表失败: {e}")

    def show_help(self):
        """显示帮助信息"""
        help_text = """
文档处理器 GUI 使用说明

1. 单文档处理：
   - 选择要处理的文档文件（支持PDF、Word、Markdown、文本）
   - 选择处理选项（提取为Markdown、同时生成Anki）
   - 点击"开始处理"

2. 批量处理：
   - 选择包含文档的目录
   - 设置处理选项（递归处理、跳过已处理）
   - 点击"开始批量处理"

3. 项目管理：
   - 查看所有已处理的项目
   - 搜索和过滤项目
   - 查看项目详情、打开文件夹、删除项目

4. Anki生成：
   - 输入项目名称（支持模糊匹配）
   - 选择交互模式
   - 或直接选择Markdown文件转换

5. 设置：
   - 配置AI服务参数
   - 调整处理参数

支持的文档格式：PDF、Word(.docx/.doc)、Markdown、文本文件
        """

        dialog = tk.Toplevel(self.root)
        dialog.title("使用说明")
        dialog.geometry("600x500")
        dialog.transient(self.root)

        text_widget = scrolledtext.ScrolledText(dialog, wrap=tk.WORD)
        text_widget.pack(fill="both", expand=True, padx=10, pady=10)
        text_widget.insert(tk.END, help_text)
        text_widget.config(state="disabled")

        ttk.Button(dialog, text="关闭", command=dialog.destroy).pack(pady=10)

    def show_about(self):
        """显示关于信息"""
        about_text = """
文档处理器 GUI v2.2

一个强大的文档处理工具，支持多种文档格式的提取和转换。

主要功能：
• 多格式文档处理（PDF、Word、Markdown、文本）
• 智能图片提取和定位
• AI驱动的Anki闪卡生成
• 项目化管理
• 批量处理

开发者：Document Processor Team
版本：v2.2
更新日期：2025-06-15
        """

        messagebox.showinfo("关于", about_text)

    def on_closing(self):
        """窗口关闭事件"""
        if self.processing:
            result = messagebox.askyesno("确认退出", "正在处理中，确定要退出吗？")
            if not result:
                return

        self.root.destroy()

    # ==================== 新增功能方法 ====================

    def setup_drag_and_drop(self):
        """设置拖拽功能"""
        try:
            # 尝试导入tkinterdnd2
            from tkinterdnd2 import DND_FILES

            # 为文件输入框设置拖拽（延迟到组件创建后）
            self.root.after(100, self._setup_drag_targets)

            self.log_message("拖拽功能已启用")

        except ImportError:
            self.log_message("拖拽功能不可用，可选安装: pip install tkinterdnd2", level="INFO")
        except Exception as e:
            self.log_message(f"拖拽功能设置失败: {e}", level="WARNING")

    def _setup_drag_targets(self):
        """设置拖拽目标（延迟执行）"""
        try:
            from tkinterdnd2 import DND_FILES

            # 为文件输入框设置拖拽
            if hasattr(self, 'file_entry'):
                self.file_entry.drop_target_register(DND_FILES)
                self.file_entry.dnd_bind('<<Drop>>', self.on_file_drop)

            if hasattr(self, 'dir_entry'):
                self.dir_entry.drop_target_register(DND_FILES)
                self.dir_entry.dnd_bind('<<Drop>>', self.on_dir_drop)

            if hasattr(self, 'md_file_entry'):
                self.md_file_entry.drop_target_register(DND_FILES)
                self.md_file_entry.dnd_bind('<<Drop>>', self.on_md_file_drop)

        except Exception as e:
            self.log_message(f"设置拖拽目标失败: {e}", level="WARNING")

    def on_file_drop(self, event):
        """处理文件拖拽"""
        files = self.root.tk.splitlist(event.data)
        if files:
            file_path = files[0]
            if os.path.isfile(file_path):
                self.selected_file_var.set(file_path)
                self.log_message(f"拖拽文件: {file_path}")
                # 自动切换到单文档处理标签页
                self.notebook.select(0)

    def on_dir_drop(self, event):
        """处理目录拖拽"""
        files = self.root.tk.splitlist(event.data)
        if files:
            dir_path = files[0]
            if os.path.isdir(dir_path):
                self.selected_dir_var.set(dir_path)
                self.log_message(f"拖拽目录: {dir_path}")
                # 自动切换到批量处理标签页
                self.notebook.select(1)

    def on_md_file_drop(self, event):
        """处理Markdown文件拖拽"""
        files = self.root.tk.splitlist(event.data)
        if files:
            file_path = files[0]
            if os.path.isfile(file_path) and file_path.lower().endswith('.md'):
                self.md_file_var.set(file_path)
                self.log_message(f"拖拽Markdown文件: {file_path}")
                # 自动切换到Markdown转Anki标签页
                self.notebook.select(3)

    def generate_anki_for_selected_project(self):
        """为选中的项目生成Anki闪卡"""
        selection = self.project_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择一个项目")
            return

        if self.processing:
            messagebox.showwarning("处理中", "已有处理任务在进行中，请等待完成")
            return

        project_name = self.project_tree.item(selection[0])['text']

        # 确认生成
        result = messagebox.askyesno(
            "确认生成",
            f"确定要为项目 '{project_name}' 生成Anki闪卡吗？"
        )

        if result:
            # 启动处理线程
            self.start_processing("generate_anki", {
                'project_name': project_name,
                'interactive': False  # 项目管理中直接生成，不需要交互
            })

    def select_all_projects(self):
        """全选所有项目"""
        all_items = self.project_tree.get_children()
        self.project_tree.selection_set(all_items)
        self.update_selection_status()

    def deselect_all_projects(self):
        """取消选择所有项目"""
        self.project_tree.selection_remove(self.project_tree.selection())
        self.update_selection_status()

    def on_project_selection_changed(self, event):
        """项目选择变化事件处理"""
        self.update_selection_status()

    def update_selection_status(self):
        """更新选择状态显示"""
        selected_count = len(self.project_tree.selection())
        if selected_count == 0:
            self.selection_status_var.set("未选择项目")
        elif selected_count == 1:
            self.selection_status_var.set("已选择 1 个项目")
        else:
            self.selection_status_var.set(f"已选择 {selected_count} 个项目")

    def batch_generate_anki(self):
        """批量生成Anki闪卡"""
        selection = self.project_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择要生成Anki的项目")
            return

        if self.processing:
            messagebox.showwarning("处理中", "已有处理任务在进行中，请等待完成")
            return

        # 获取选中的项目名称
        selected_projects = []
        for item_id in selection:
            project_name = self.project_tree.item(item_id)['text']
            selected_projects.append(project_name)

        # 确认批量生成
        result = messagebox.askyesno(
            "确认批量生成",
            f"确定要为以下 {len(selected_projects)} 个项目生成Anki闪卡吗？\n\n" +
            "\n".join(f"• {name}" for name in selected_projects[:10]) +
            (f"\n... 还有 {len(selected_projects) - 10} 个项目" if len(selected_projects) > 10 else "")
        )

        if result:
            # 启动批量处理线程
            self.start_processing("batch_generate_anki", {
                'project_names': selected_projects
            })



    def load_settings_from_config(self):
        """从config.ini加载设置"""
        config_path = "config.ini"
        if not os.path.exists(config_path):
            self.log_message("配置文件不存在，使用默认设置")
            return

        try:
            import configparser
            parser = configparser.ConfigParser()
            parser.read(config_path, encoding="utf-8")

            if "DEFAULT" in parser:
                cfg = parser["DEFAULT"]

                # 加载处理设置
                self.pdf_top_margin_var.set(cfg.get('PDF_TOP_MARGIN', '50.0'))
                self.pdf_bottom_margin_var.set(cfg.get('PDF_BOTTOM_MARGIN', '50.0'))
                self.chunk_size_var.set(cfg.get('CHUNK_SIZE', '500'))
                self.chunk_stride_var.set(cfg.get('CHUNK_STRIDE', '450'))
                self.lines_to_skip_var.set(cfg.get('LINES_TO_SKIP', '13'))

                # 加载智能分块设置
                if hasattr(self, 'enable_smart_chunking_var'):
                    enable_chunking = cfg.get('ENABLE_SMART_CHUNKING', 'false').lower() == 'true'
                    self.enable_smart_chunking_var.set(enable_chunking)

                if hasattr(self, 'validation_threshold_var'):
                    self.validation_threshold_var.set(cfg.get('CONTENT_VALIDATION_THRESHOLD', '0.8'))

                if hasattr(self, 'max_retries_var'):
                    self.max_retries_var.set(cfg.get('MAX_CONTENT_RETRIES', '2'))

                # 加载AI分块设置
                if hasattr(self, 'enable_ai_chunking_var'):
                    enable_ai_chunking = cfg.get('ENABLE_AI_CHUNKING', 'true').lower() == 'true'
                    self.enable_ai_chunking_var.set(enable_ai_chunking)

                if hasattr(self, 'ai_chunking_target_size_var'):
                    self.ai_chunking_target_size_var.set(cfg.get('AI_CHUNKING_TARGET_SIZE', '12000'))

                if hasattr(self, 'ai_chunking_max_content_var'):
                    self.ai_chunking_max_content_var.set(cfg.get('AI_CHUNKING_MAX_CONTENT_SIZE', '50000'))

                if hasattr(self, 'ai_chunking_timeout_var'):
                    self.ai_chunking_timeout_var.set(cfg.get('AI_CHUNKING_TIMEOUT', '180'))

                if hasattr(self, 'ai_chunking_fallback_var'):
                    enable_fallback = cfg.get('AI_CHUNKING_FALLBACK_TO_RULES', 'true').lower() == 'true'
                    self.ai_chunking_fallback_var.set(enable_fallback)

                self.log_message("设置已从配置文件加载")

        except Exception as e:
            self.log_message(f"加载配置文件失败: {e}", level="ERROR")

    def show_failed_blocks_stats(self):
        """显示失败块统计信息"""
        try:
            if not self.processor or not hasattr(self.processor, 'ai_service'):
                messagebox.showwarning("警告", "AI服务未初始化")
                return

            # 获取失败块统计
            stats = self.processor.ai_service.get_failed_blocks_stats()

            # 创建统计信息窗口
            stats_window = tk.Toplevel(self.root)
            stats_window.title("失败块统计信息")
            stats_window.geometry("500x400")
            stats_window.transient(self.root)
            stats_window.grab_set()

            # 统计信息文本
            stats_text = scrolledtext.ScrolledText(stats_window, wrap=tk.WORD)
            stats_text.pack(fill="both", expand=True, padx=10, pady=10)

            if stats['total_failed'] == 0:
                stats_text.insert(tk.END, "✅ 没有失败的块\n")
            else:
                stats_text.insert(tk.END, f"📊 失败块统计信息\n")
                stats_text.insert(tk.END, f"{'='*50}\n\n")
                stats_text.insert(tk.END, f"总失败数: {stats['total_failed']}\n")
                stats_text.insert(tk.END, f"可重试数: {stats['retry_candidates']}\n\n")

                stats_text.insert(tk.END, "按失败类型统计:\n")
                for failure_type, count in stats['by_type'].items():
                    stats_text.insert(tk.END, f"  {failure_type}: {count}\n")

                stats_text.insert(tk.END, "\n按项目统计:\n")
                for project, count in stats['by_project'].items():
                    stats_text.insert(tk.END, f"  {project}: {count}\n")

                stats_text.insert(tk.END, f"\n日志文件: {stats['log_file']}\n")
                stats_text.insert(tk.END, f"文件存在: {'是' if stats['file_exists'] else '否'}\n")

            stats_text.config(state=tk.DISABLED)

            # 关闭按钮
            ttk.Button(stats_window, text="关闭",
                      command=stats_window.destroy).pack(pady=10)

        except Exception as e:
            self.log_message(f"显示失败块统计失败: {e}", level="ERROR")
            messagebox.showerror("错误", f"显示失败块统计失败: {e}")

    def show_failed_blocks_manager(self):
        """显示失败块管理器"""
        try:
            if not self.processor or not hasattr(self.processor, 'ai_service'):
                messagebox.showwarning("警告", "AI服务未初始化")
                return

            # 获取失败块列表
            failed_blocks = self.processor.ai_service.failed_block_logger.get_failed_blocks()

            # 创建管理器窗口
            manager_window = tk.Toplevel(self.root)
            manager_window.title("失败块管理器")
            manager_window.geometry("800x600")
            manager_window.transient(self.root)
            manager_window.grab_set()

            # 创建主框架
            main_frame = ttk.Frame(manager_window)
            main_frame.pack(fill="both", expand=True, padx=10, pady=10)

            # 失败块列表
            list_frame = ttk.LabelFrame(main_frame, text="失败块列表", padding=5)
            list_frame.pack(fill="both", expand=True, pady=(0, 10))

            # 创建Treeview显示失败块
            columns = ("项目", "块索引", "失败类型", "失败原因", "尝试次数", "时间")
            tree = ttk.Treeview(list_frame, columns=columns, show="headings", height=15)

            for col in columns:
                tree.heading(col, text=col)
                tree.column(col, width=120)

            # 添加滚动条
            scrollbar = ttk.Scrollbar(list_frame, orient="vertical", command=tree.yview)
            tree.configure(yscrollcommand=scrollbar.set)

            tree.pack(side="left", fill="both", expand=True)
            scrollbar.pack(side="right", fill="y")

            # 填充数据
            for block in failed_blocks:
                tree.insert("", "end", values=(
                    block.project_name,
                    block.block_index,
                    block.failure_type,
                    block.failure_reason[:50] + "..." if len(block.failure_reason) > 50 else block.failure_reason,
                    block.attempt_count,
                    block.timestamp[:19]  # 只显示日期时间部分
                ))

            # 操作按钮
            button_frame = ttk.Frame(main_frame)
            button_frame.pack(fill="x")

            def retry_selected_blocks():
                """重试选中的块"""
                selection = tree.selection()
                if not selection:
                    messagebox.showwarning("警告", "请先选择要重试的块")
                    return

                # 这里可以实现重试逻辑
                messagebox.showinfo("信息", f"重试功能开发中，选中了 {len(selection)} 个块")

            def retry_all_failed_blocks():
                """重试所有失败的块"""
                if not failed_blocks:
                    messagebox.showinfo("信息", "没有失败的块需要重试")
                    return

                # 这里可以实现批量重试逻辑
                messagebox.showinfo("信息", f"批量重试功能开发中，共 {len(failed_blocks)} 个失败块")

            def clear_failed_blocks_log():
                """清理失败块日志"""
                if not failed_blocks:
                    messagebox.showinfo("信息", "没有失败块需要清理")
                    return

                confirm = messagebox.askyesno("确认", f"确定要清理所有 {len(failed_blocks)} 条失败记录吗？")
                if confirm:
                    try:
                        removed_count = self.processor.ai_service.failed_block_logger.clear_log()
                        messagebox.showinfo("成功", f"已清理 {removed_count} 条记录")
                        manager_window.destroy()
                    except Exception as e:
                        messagebox.showerror("错误", f"清理失败: {e}")

            ttk.Button(button_frame, text="重试选中的块",
                      command=retry_selected_blocks).pack(side="left", padx=(0, 10))
            ttk.Button(button_frame, text="重试所有可重试的块",
                      command=retry_all_failed_blocks).pack(side="left", padx=(0, 10))
            ttk.Button(button_frame, text="清理失败块日志",
                      command=clear_failed_blocks_log).pack(side="left", padx=(0, 10))
            ttk.Button(button_frame, text="关闭",
                      command=manager_window.destroy).pack(side="right")

        except Exception as e:
            self.log_message(f"显示失败块管理器失败: {e}", level="ERROR")
            messagebox.showerror("错误", f"显示失败块管理器失败: {e}")

    def show_chunking_preview(self):
        """显示分块预览"""
        try:
            # 项目选择对话框
            projects = self.get_project_list()
            if not projects:
                messagebox.showinfo("信息", "没有找到项目")
                return

            # 创建项目选择对话框
            dialog = tk.Toplevel(self.root)
            dialog.title("选择项目进行分块预览")
            dialog.geometry("400x300")
            dialog.transient(self.root)
            dialog.grab_set()

            ttk.Label(dialog, text="选择要预览分块的项目:").pack(pady=10)

            # 项目列表
            listbox = tk.Listbox(dialog, height=10)
            listbox.pack(fill="both", expand=True, padx=10, pady=5)

            for project in projects:
                listbox.insert(tk.END, project)

            selected_project = [None]

            def on_select():
                selection = listbox.curselection()
                if selection:
                    selected_project[0] = projects[selection[0]]
                    dialog.destroy()

            def on_cancel():
                dialog.destroy()

            button_frame = ttk.Frame(dialog)
            button_frame.pack(fill="x", padx=10, pady=10)

            ttk.Button(button_frame, text="预览", command=on_select).pack(side="left")
            ttk.Button(button_frame, text="取消", command=on_cancel).pack(side="right")

            # 双击选择
            listbox.bind("<Double-Button-1>", lambda e: on_select())

            dialog.wait_window()

            if selected_project[0]:
                self.show_project_chunking_preview(selected_project[0])

        except Exception as e:
            self.log_message(f"显示分块预览失败: {e}", level="ERROR")
            messagebox.showerror("错误", f"显示分块预览失败: {e}")

    def show_project_chunking_preview(self, project_name: str):
        """显示项目的分块预览"""
        try:
            if not self.processor or not hasattr(self.processor, 'semantic_chunker'):
                messagebox.showwarning("警告", "智能分块功能未初始化")
                return

            # 查找项目的预处理文件
            base_dir = Path(self.processor.config.get('BASE_DIR', 'documents'))
            try:
                preprocessed_file_path = self.processor.semantic_chunker.get_preprocessed_file_path(project_name, base_dir)
            except FileNotFoundError as e:
                messagebox.showwarning("警告", f"项目 '{project_name}' 中没有找到markdown文件")
                return

            # 创建预览窗口
            preview_window = tk.Toplevel(self.root)
            preview_window.title(f"项目 '{project_name}' 的分块预览")
            preview_window.geometry("900x700")
            preview_window.transient(self.root)
            preview_window.grab_set()

            # 创建主框架
            main_frame = ttk.Frame(preview_window)
            main_frame.pack(fill="both", expand=True, padx=10, pady=10)

            # 状态信息框架
            status_frame = ttk.LabelFrame(main_frame, text="分块状态", padding=5)
            status_frame.pack(fill="x", pady=(0, 10))

            if preprocessed_file_path.exists():
                # 加载预处理内容
                content = self.processor.semantic_chunker.load_preprocessed_content(preprocessed_file_path)
                if content:
                    # 获取分块统计
                    stats = self.processor.chunk_processor.get_chunk_statistics(content)

                    # 显示统计信息
                    status_text = f"✅ 已预处理 | 总长度: {stats['total_content_length']:,} 字符 | "
                    status_text += f"语义块: {stats['effective_chunks']} | "
                    status_text += f"验证: {'通过' if stats['validation']['valid'] else '失败'}"

                    ttk.Label(status_frame, text=status_text).pack(anchor="w")

                    # 分块预览
                    preview_frame = ttk.LabelFrame(main_frame, text="分块预览", padding=5)
                    preview_frame.pack(fill="both", expand=True, pady=(0, 10))

                    # 创建Treeview显示分块
                    columns = ("块号", "长度", "行数", "预览")
                    tree = ttk.Treeview(preview_frame, columns=columns, show="headings", height=15)

                    tree.heading("块号", text="块号")
                    tree.heading("长度", text="长度(字符)")
                    tree.heading("行数", text="行数")
                    tree.heading("预览", text="内容预览")

                    tree.column("块号", width=60)
                    tree.column("长度", width=100)
                    tree.column("行数", width=80)
                    tree.column("预览", width=500)

                    # 添加滚动条
                    scrollbar = ttk.Scrollbar(preview_frame, orient="vertical", command=tree.yview)
                    tree.configure(yscrollcommand=scrollbar.set)

                    tree.pack(side="left", fill="both", expand=True)
                    scrollbar.pack(side="right", fill="y")

                    # 获取分块预览
                    previews = self.processor.chunk_processor.preview_chunks(content, 100)

                    for preview in previews:
                        tree.insert("", "end", values=(
                            preview['index'] + 1,
                            f"{preview['length']:,}",
                            preview['lines'],
                            preview['preview'].replace('\n', ' ')[:100] + "..."
                        ))

                    # 操作按钮
                    button_frame = ttk.Frame(main_frame)
                    button_frame.pack(fill="x")

                    def reprocess_project():
                        """重新预处理项目"""
                        confirm = messagebox.askyesno("确认", f"确定要重新预处理项目 '{project_name}' 吗？")
                        if confirm:
                            try:
                                # 删除现有预处理文件
                                if preprocessed_file_path.exists():
                                    preprocessed_file_path.unlink()

                                messagebox.showinfo("成功", "预处理文件已删除，下次处理时将重新生成")
                                preview_window.destroy()

                            except Exception as e:
                                messagebox.showerror("错误", f"删除预处理文件失败: {e}")

                    ttk.Button(button_frame, text="重新预处理", command=reprocess_project).pack(side="left", padx=(0, 10))
                    ttk.Button(button_frame, text="关闭", command=preview_window.destroy).pack(side="right")

                else:
                    ttk.Label(status_frame, text="❌ 无法读取预处理文件").pack(anchor="w")
                    ttk.Button(main_frame, text="关闭", command=preview_window.destroy).pack(pady=10)
            else:
                # 没有预处理文件
                ttk.Label(status_frame, text="⚠️ 项目尚未进行语义分块预处理").pack(anchor="w")

                # 提供预处理选项
                action_frame = ttk.LabelFrame(main_frame, text="操作选项", padding=10)
                action_frame.pack(fill="both", expand=True, pady=(0, 10))

                ttk.Label(action_frame, text="此项目尚未进行语义分块预处理。").pack(anchor="w", pady=5)
                ttk.Label(action_frame, text="您可以：").pack(anchor="w", pady=5)
                ttk.Label(action_frame, text="1. 启用智能分块模式后重新处理项目").pack(anchor="w", padx=20)
                ttk.Label(action_frame, text="2. 使用下面的按钮进行单独预处理").pack(anchor="w", padx=20)

                def preprocess_only():
                    """只进行预处理"""
                    preview_window.destroy()
                    self.preprocess_markdown_only(project_name)

                button_frame = ttk.Frame(main_frame)
                button_frame.pack(fill="x")

                ttk.Button(button_frame, text="只预处理MD", command=preprocess_only).pack(side="left", padx=(0, 10))
                ttk.Button(button_frame, text="关闭", command=preview_window.destroy).pack(side="right")

        except Exception as e:
            self.log_message(f"显示项目分块预览失败: {e}", level="ERROR")
            messagebox.showerror("错误", f"显示项目分块预览失败: {e}")

    def reload_config(self):
        """重新加载配置"""
        try:
            # 重新初始化处理器以应用新配置
            self.init_processor()
            self.log_message("配置已重新加载")
        except Exception as e:
            self.log_message(f"重新加载配置失败: {e}", level="ERROR")

    def get_project_list(self):
        """获取项目列表"""
        try:
            if not self.processor:
                return []

            # 获取documents/extracted目录下的所有项目
            base_dir = Path(self.processor.config.get('BASE_DIR', 'documents'))
            extracted_dir = base_dir / 'extracted'

            if not extracted_dir.exists():
                return []

            projects = []
            for item in extracted_dir.iterdir():
                if item.is_dir():
                    projects.append(item.name)

            return sorted(projects)

        except Exception as e:
            self.log_message(f"获取项目列表失败: {e}", level="ERROR")
            return []

    def preprocess_markdown_only(self, project_name: str):
        """只预处理Markdown文件"""
        try:
            if not self.processor or not hasattr(self.processor, 'semantic_chunker'):
                messagebox.showwarning("警告", "智能分块功能未初始化")
                return

            # 确认对话框
            confirm = messagebox.askyesno("确认", f"确定要对项目 '{project_name}' 进行语义分块预处理吗？\n\n这将分析文档内容并插入语义分块标记。")
            if not confirm:
                return

            # 创建进度窗口
            progress_window = tk.Toplevel(self.root)
            progress_window.title("预处理进度")
            progress_window.geometry("400x200")
            progress_window.transient(self.root)
            progress_window.grab_set()

            # 进度信息
            progress_frame = ttk.Frame(progress_window)
            progress_frame.pack(fill="both", expand=True, padx=20, pady=20)

            status_label = ttk.Label(progress_frame, text="正在准备预处理...")
            status_label.pack(pady=10)

            progress_bar = ttk.Progressbar(progress_frame, mode='indeterminate')
            progress_bar.pack(fill="x", pady=10)
            progress_bar.start()

            log_text = scrolledtext.ScrolledText(progress_frame, height=6, wrap=tk.WORD)
            log_text.pack(fill="both", expand=True, pady=10)

            def update_log(message):
                log_text.insert(tk.END, f"{message}\n")
                log_text.see(tk.END)
                progress_window.update()

            def run_preprocessing():
                try:
                    update_log(f"开始预处理项目: {project_name}")

                    # 查找项目的markdown文件
                    base_dir = Path(self.processor.config.get('BASE_DIR', 'documents'))
                    project_dir = base_dir / 'extracted' / project_name

                    if not project_dir.exists():
                        update_log(f"错误: 项目目录不存在: {project_dir}")
                        return False

                    # 查找markdown文件
                    md_files = list(project_dir.glob("*.md"))
                    if not md_files:
                        update_log("错误: 项目中没有找到markdown文件")
                        return False

                    md_file = md_files[0]  # 使用第一个markdown文件
                    update_log(f"找到markdown文件: {md_file.name}")

                    # 读取markdown内容
                    with open(md_file, 'r', encoding='utf-8') as f:
                        content = f.read()

                    update_log(f"文件大小: {len(content):,} 字符")

                    # 执行语义分块预处理
                    status_label.config(text="正在进行AI语义分块...")
                    update_log("调用AI服务进行语义分块...")

                    chunking_result = self.processor.semantic_chunker.preprocess_document(content, project_name)

                    if chunking_result.success:
                        update_log(f"✅ 预处理成功!")
                        update_log(f"生成语义块数量: {chunking_result.chunk_count}")
                        update_log(f"处理时间: {chunking_result.processing_time:.2f}秒")
                        update_log(f"原始长度: {chunking_result.original_length:,} 字符")
                        update_log(f"处理后长度: {chunking_result.preprocessed_length:,} 字符")

                        # 保存预处理结果
                        preprocessed_file_path = self.processor.semantic_chunker.get_preprocessed_file_path(project_name, base_dir)
                        if self.processor.semantic_chunker.save_preprocessed_content(chunking_result.preprocessed_content, preprocessed_file_path):
                            update_log(f"预处理文件已保存: {preprocessed_file_path.name}")
                        else:
                            update_log("警告: 预处理文件保存失败")

                        return True
                    else:
                        update_log(f"❌ 预处理失败: {chunking_result.error_message}")
                        return False

                except Exception as e:
                    update_log(f"❌ 预处理异常: {str(e)}")
                    return False

            # 在后台线程中运行预处理
            def background_task():
                try:
                    success = run_preprocessing()

                    progress_bar.stop()

                    if success:
                        status_label.config(text="预处理完成!")
                        # 添加关闭按钮
                        ttk.Button(progress_frame, text="关闭",
                                 command=progress_window.destroy).pack(pady=10)
                    else:
                        status_label.config(text="预处理失败!")
                        ttk.Button(progress_frame, text="关闭",
                                 command=progress_window.destroy).pack(pady=10)

                except Exception as e:
                    progress_bar.stop()
                    status_label.config(text="预处理异常!")
                    update_log(f"异常: {str(e)}")
                    ttk.Button(progress_frame, text="关闭",
                             command=progress_window.destroy).pack(pady=10)

            # 启动后台任务
            threading.Thread(target=background_task, daemon=True).start()

        except Exception as e:
            self.log_message(f"预处理Markdown失败: {e}", level="ERROR")
            messagebox.showerror("错误", f"预处理Markdown失败: {e}")

    def run(self):
        """运行GUI应用程序"""
        # 初始化时刷新项目列表
        self.refresh_project_list()

        # 启动主循环
        self.root.mainloop()


def main():
    """主函数"""
    try:
        # 尝试使用支持拖拽的tkinter
        from tkinterdnd2 import TkinterDnD

        # 创建支持拖拽的GUI应用程序
        class DragDropGUI(DocumentProcessorGUI):
            def __init__(self):
                # 创建支持拖拽的根窗口
                self.root = TkinterDnD.Tk()
                self.root.title("文档处理器 - Document Processor GUI")
                self.root.geometry("1000x700")
                self.root.minsize(800, 600)

                # 初始化其他组件
                self.processor = None
                self.message_queue = __import__('queue').Queue()
                self.processing = False
                self.current_operation = None

                # 创建GUI组件
                self.create_widgets()
                self.setup_layout()
                self.setup_menu()

                # 初始化后端处理器
                self.init_processor()

                # 启动消息处理循环
                self.process_queue()

                # 设置拖拽功能
                self.setup_drag_and_drop()

                # 加载设置
                self.load_settings_from_config()

                # 设置窗口关闭事件
                self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

        # 创建并运行拖拽版GUI
        app = DragDropGUI()
        app.run()

    except ImportError:
        # 如果没有tkinterdnd2，使用普通版本
        print("提示：安装 tkinterdnd2 可启用拖拽功能: pip install tkinterdnd2")
        app = DocumentProcessorGUI()
        app.run()

    except Exception as e:
        print(f"启动拖拽版本失败: {e}")
        print("使用普通版本...")
        app = DocumentProcessorGUI()
        app.run()


if __name__ == "__main__":
    main()
