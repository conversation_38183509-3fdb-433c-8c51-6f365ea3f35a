#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
prompt_manager.py

提示词管理系统，支持多种题库类型和答案格式
"""

import json
import logging
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum


class QuestionType(Enum):
    """题目类型枚举"""
    FILL_IN_BLANK = "fill_in_blank"  # 填空题
    SHORT_ANSWER = "short_answer"    # 简答题
    MULTIPLE_CHOICE = "multiple_choice"  # 选择题
    TRUE_FALSE = "true_false"        # 判断题
    ESSAY = "essay"                  # 论述题
    CALCULATION = "calculation"      # 计算题


class AnswerFormat(Enum):
    """答案格式枚举"""
    REFERENCE_ANSWER = "reference_answer"  # "参考答案"字样
    BOLD_RED = "bold_red"                 # 标红加粗
    ANSWER_COLON = "answer_colon"         # "答案："格式
    SOLUTION_COLON = "solution_colon"     # "解："格式
    CUSTOM = "custom"                     # 自定义格式


@dataclass
class PromptTemplate:
    """提示词模板"""
    name: str
    description: str
    question_type: QuestionType
    answer_format: AnswerFormat
    system_prompt: str
    task_prompt: str
    example_input: str = ""
    example_output: str = ""
    custom_patterns: List[str] = None  # 自定义答案识别模式


class PromptManager:
    """提示词管理器"""
    
    def __init__(self, config_file: str = "prompt_templates.json"):
        """
        初始化提示词管理器
        
        Args:
            config_file: 提示词配置文件路径
        """
        self.config_file = config_file
        self.templates: Dict[str, PromptTemplate] = {}
        self.current_template: Optional[str] = None
        
        # 加载提示词模板
        self._load_templates()
        
        # 如果没有模板，创建默认模板
        if not self.templates:
            self._create_default_templates()
            self._save_templates()
    
    def _load_templates(self):
        """加载提示词模板"""
        if not Path(self.config_file).exists():
            return
        
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            for template_data in data.get('templates', []):
                template = PromptTemplate(
                    name=template_data['name'],
                    description=template_data['description'],
                    question_type=QuestionType(template_data['question_type']),
                    answer_format=AnswerFormat(template_data['answer_format']),
                    system_prompt=template_data['system_prompt'],
                    task_prompt=template_data['task_prompt'],
                    example_input=template_data.get('example_input', ''),
                    example_output=template_data.get('example_output', ''),
                    custom_patterns=template_data.get('custom_patterns', [])
                )
                self.templates[template.name] = template
            
            self.current_template = data.get('current_template')
            logging.info(f"加载了 {len(self.templates)} 个提示词模板")
            
        except Exception as e:
            logging.error(f"加载提示词模板失败: {e}")
    
    def _save_templates(self):
        """保存提示词模板"""
        try:
            data = {
                'current_template': self.current_template,
                'templates': []
            }
            
            for template in self.templates.values():
                template_data = {
                    'name': template.name,
                    'description': template.description,
                    'question_type': template.question_type.value,
                    'answer_format': template.answer_format.value,
                    'system_prompt': template.system_prompt,
                    'task_prompt': template.task_prompt,
                    'example_input': template.example_input,
                    'example_output': template.example_output,
                    'custom_patterns': template.custom_patterns or []
                }
                data['templates'].append(template_data)
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            logging.info(f"保存了 {len(self.templates)} 个提示词模板")
            
        except Exception as e:
            logging.error(f"保存提示词模板失败: {e}")
    
    def _create_default_templates(self):
        """创建默认提示词模板"""
        # 默认模板1：参考答案格式
        default_template_1 = PromptTemplate(
            name="default_reference_answer",
            description="默认模板 - 参考答案格式",
            question_type=QuestionType.FILL_IN_BLANK,
            answer_format=AnswerFormat.REFERENCE_ANSWER,
            system_prompt="""你是一个专业的教育内容分析师，专门从学习材料中提取问答对。你的任务是识别文本中的问题和对应的答案，并将它们转换为结构化的JSON格式。

请严格按照以下要求：
1. 只提取明确的问答对，不要创造或推测内容
2. 问题必须完整且有意义
3. 答案必须准确对应问题
4. 保持原文的准确性和完整性
5. 如果文本中没有明确的问答对，返回空数组""",
            task_prompt="""请从以下文本中提取问答对。文本中的答案通常以"参考答案"字样标识。

要求：
- 识别所有问题和对应的参考答案
- 确保问答对的准确性和完整性
- 输出格式为JSON数组，每个元素包含question和answer字段

输出格式示例：
[
  {
    "question": "问题内容",
    "answer": "答案内容"
  }
]""",
            example_input="1. 什么是人工智能？\n参考答案：人工智能是计算机科学的一个分支，致力于创建能够执行通常需要人类智能的任务的系统。",
            example_output='[{"question": "什么是人工智能？", "answer": "人工智能是计算机科学的一个分支，致力于创建能够执行通常需要人类智能的任务的系统。"}]'
        )
        
        # 默认模板2：标红加粗格式
        default_template_2 = PromptTemplate(
            name="default_bold_red",
            description="默认模板 - 标红加粗格式",
            question_type=QuestionType.FILL_IN_BLANK,
            answer_format=AnswerFormat.BOLD_RED,
            system_prompt="""你是一个专业的教育内容分析师，专门从学习材料中提取问答对。你的任务是识别文本中的问题和对应的答案，并将它们转换为结构化的JSON格式。

请严格按照以下要求：
1. 只提取明确的问答对，不要创造或推测内容
2. 问题必须完整且有意义
3. 答案必须准确对应问题
4. 保持原文的准确性和完整性
5. 如果文本中没有明确的问答对，返回空数组""",
            task_prompt="""请从以下文本中提取问答对。文本中的答案通常以标红、加粗等格式突出显示。

要求：
- 识别所有问题和对应的突出显示的答案
- 注意识别**粗体**、*斜体*、或其他格式标记的答案
- 确保问答对的准确性和完整性
- 输出格式为JSON数组，每个元素包含question和answer字段

输出格式示例：
[
  {
    "question": "问题内容",
    "answer": "答案内容"
  }
]""",
            example_input="1. 什么是机器学习？\n**机器学习是人工智能的一个子领域，使计算机能够在没有明确编程的情况下学习。**",
            example_output='[{"question": "什么是机器学习？", "answer": "机器学习是人工智能的一个子领域，使计算机能够在没有明确编程的情况下学习。"}]'
        )
        
        # 选择题模板
        choice_template = PromptTemplate(
            name="multiple_choice",
            description="选择题模板",
            question_type=QuestionType.MULTIPLE_CHOICE,
            answer_format=AnswerFormat.ANSWER_COLON,
            system_prompt="""你是一个专业的教育内容分析师，专门从学习材料中提取选择题问答对。你的任务是识别文本中的选择题和对应的答案，并将它们转换为结构化的JSON格式。

请严格按照以下要求：
1. 只提取明确的选择题问答对，不要创造或推测内容
2. 问题必须包含完整的选项（A、B、C、D等）
3. 答案必须准确对应问题
4. 保持原文的准确性和完整性
5. 如果文本中没有明确的问答对，返回空数组""",
            task_prompt="""请从以下文本中提取选择题问答对。

要求：
- 识别所有选择题和对应的答案
- 问题应包含完整的选项内容
- 答案可能以"答案："、"正确答案："等格式标识
- 确保问答对的准确性和完整性
- 输出格式为JSON数组，每个元素包含question和answer字段

输出格式示例：
[
  {
    "question": "以下哪个是编程语言？\nA. HTML\nB. Python\nC. CSS\nD. SQL",
    "answer": "B"
  }
]""",
            example_input="1. 以下哪个是编程语言？\nA. HTML\nB. Python\nC. CSS\nD. SQL\n答案：B",
            example_output='[{"question": "以下哪个是编程语言？\\nA. HTML\\nB. Python\\nC. CSS\\nD. SQL", "answer": "B"}]'
        )
        
        # 当前使用的复杂模板（包含图片处理）
        current_complex_template = PromptTemplate(
            name="current_complex",
            description="当前复杂模板 - 支持图片处理和多种格式",
            question_type=QuestionType.MULTIPLE_CHOICE,
            answer_format=AnswerFormat.CUSTOM,
            system_prompt="""你是一个精通数据提取的AI助手，专注于从结构化和非结构化的文本中准确解析教育内容。你的任务是从给定的文本中提取完整的问答对，并严格按照指定格式返回。特别注意：你必须确保所有图片信息都被正确提取和保留，包括完整的文件路径。""",
            task_prompt="""请严格按照以下规则，分析提供的文本片段，并提取其中所有完整的'问题-答案'对：

**提取规则：**
1. **完整性优先：** 如果文本片段末尾的问题或答案不完整，必须彻底忽略该条目，绝不猜测或补全。

2. **格式保留：** front（问题）和 back（答案）字段的内容应尽量保留原始格式，如列表、代码块等。对于换行，请直接保留 \\n。

3. **图片处理（重要）：**
   - 若内容中包含HTML图片标签格式 <img alt="..." src="文件路径">，则必须在输出中保留完整的图片引用。
   - 完整保留图片的src属性中的文件路径，包括文件夹路径，如 path/to/image.png、folder\\subfolder\\image.svg 等。
   - 保持原始的HTML img标签格式不变。
   - **智能图片归属判断：** 根据图片在原文中的上下文位置，智能判断图片应该归属于问题部分（front字段）还是答案部分（back字段）：
     * 如果图片出现在题目描述、选项列表或问题相关内容中，则将图片放入 front 字段
     * 如果图片出现在答案解析、解题步骤或答案说明中，则将图片放入 back 字段
     * 允许调整图片在front/back字段中的位置，但必须保持图片与相关文字内容的逻辑关联
   - 绝对不能遗漏任何图片信息，无论图片位于文件系统的哪个位置。
   - **注意：** 只允许移动图片位置到合适的字段，严禁修改任何文字内容。

4. **内容忽略：** 在提取过程中，必须忽略所有出现的无用营销信息，特别是 "专业笔试助攻\\代做:jobhelp101" 等，以及 "## 第 3 页" 这种页数信息。

**输出格式要求：**
你必须将所有提取出的'问题-答案'对整合到一个单一的JSON对象中。此JSON对象必须包含一个根键 "questions"，其值为一个JSON对象列表。列表中的每个对象代表一道独立的题目，且必须包含 "front" 和 "back" 两个键。

注意需要保留题目中本来有的序号。
如果当前文本块中没有发现任何完整的问答对，则返回 {"questions": []}。

**JSON 格式示例（注意图片智能归属）：**
{
  "questions": [
    {
      "front": "1. <img alt=\\"电路图\\" src=\\"circuits/wye-delta.svg\\">\\n根据上图所示的电路连接方式，这种配置被称为？\\n- A. 星形连接\\n- B. 三角形连接\\n- C. 串联连接\\n- D. 并联连接",
      "back": "答案：B. 三角形连接\\n\\n解析：从图中可以看出这是典型的三角形（Delta）连接方式，三个绕组首尾相连形成闭合回路。"
    }
  ]
}

**重要提醒：**
- 必须确保所有出现在文本中的 <img> 标签都被完整保留在输出中
- 完整保留src属性中的文件路径，包括文件夹结构（可能使用 / 或 \\ 分隔符）
- 保持HTML img标签的原始格式不变
- **智能判断图片归属：** 根据图片的上下文内容，将图片放置在最合适的字段中（front或back）
- **图片与题目关联：** 问题相关的图片放在front字段，答案解析相关的图片放在back字段
- 允许调整图片标签的位置，但严禁修改任何文字内容
- 绝对不能遗漏任何图片信息
- 只返回JSON格式的数据，不要包含任何其他文字说明""",
            custom_patterns=["参考答案", "答案：", "解析：", "**", "*"]
        )

        self.templates[default_template_1.name] = default_template_1
        self.templates[default_template_2.name] = default_template_2
        self.templates[choice_template.name] = choice_template
        self.templates[current_complex_template.name] = current_complex_template

        # 设置当前复杂模板为默认
        self.current_template = current_complex_template.name
    
    def get_template(self, name: str) -> Optional[PromptTemplate]:
        """获取指定名称的模板"""
        return self.templates.get(name)
    
    def get_current_template(self) -> Optional[PromptTemplate]:
        """获取当前使用的模板"""
        if self.current_template:
            return self.templates.get(self.current_template)
        return None
    
    def set_current_template(self, name: str) -> bool:
        """设置当前使用的模板"""
        if name in self.templates:
            self.current_template = name
            self._save_templates()
            logging.info(f"切换到提示词模板: {name}")
            return True
        return False
    
    def list_templates(self) -> List[Dict[str, str]]:
        """列出所有模板的基本信息"""
        return [
            {
                'name': template.name,
                'description': template.description,
                'question_type': template.question_type.value,
                'answer_format': template.answer_format.value,
                'is_current': template.name == self.current_template
            }
            for template in self.templates.values()
        ]
    
    def add_template(self, template: PromptTemplate) -> bool:
        """添加新模板"""
        try:
            self.templates[template.name] = template
            self._save_templates()
            logging.info(f"添加新提示词模板: {template.name}")
            return True
        except Exception as e:
            logging.error(f"添加模板失败: {e}")
            return False
    
    def delete_template(self, name: str) -> bool:
        """删除模板"""
        if name in self.templates:
            del self.templates[name]
            if self.current_template == name:
                # 如果删除的是当前模板，切换到第一个可用模板
                if self.templates:
                    self.current_template = next(iter(self.templates.keys()))
                else:
                    self.current_template = None
            self._save_templates()
            logging.info(f"删除提示词模板: {name}")
            return True
        return False
    
    def get_current_prompts(self) -> Tuple[str, str]:
        """获取当前模板的系统提示词和任务提示词"""
        template = self.get_current_template()
        if template:
            return template.system_prompt, template.task_prompt
        
        # 如果没有当前模板，返回默认提示词
        return self._get_fallback_prompts()
    
    def _get_fallback_prompts(self) -> Tuple[str, str]:
        """获取后备提示词（当没有可用模板时）"""
        system_prompt = """你是一个专业的教育内容分析师，专门从学习材料中提取问答对。你的任务是识别文本中的问题和对应的答案，并将它们转换为结构化的JSON格式。

请严格按照以下要求：
1. 只提取明确的问答对，不要创造或推测内容
2. 问题必须完整且有意义
3. 答案必须准确对应问题
4. 保持原文的准确性和完整性
5. 如果文本中没有明确的问答对，返回空数组"""

        task_prompt = """请从以下文本中提取问答对，并以JSON格式输出。

要求：
- 识别所有明确的问题和对应的答案
- 确保问答对的准确性和完整性
- 输出格式为JSON数组，每个元素包含question和answer字段

输出格式示例：
[
  {
    "question": "问题内容",
    "answer": "答案内容"
  }
]"""
        
        return system_prompt, task_prompt


def main():
    """测试提示词管理器"""
    logging.basicConfig(level=logging.INFO)
    
    manager = PromptManager()
    
    print("可用的提示词模板:")
    for template_info in manager.list_templates():
        current_mark = " (当前)" if template_info['is_current'] else ""
        print(f"  - {template_info['name']}: {template_info['description']}{current_mark}")
    
    print(f"\n当前模板: {manager.current_template}")
    
    # 获取当前提示词
    system_prompt, task_prompt = manager.get_current_prompts()
    print(f"\n系统提示词长度: {len(system_prompt)} 字符")
    print(f"任务提示词长度: {len(task_prompt)} 字符")


if __name__ == "__main__":
    main()
