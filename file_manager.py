#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
file_manager.py

统一的文件命名管理系统，支持多种文档类型的处理
"""

import os
import re
import datetime
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Union
import logging


class FileManager:
    """文件命名和路径管理器"""
    
    def __init__(self, base_dir: str = "documents"):
        """
        初始化文件管理器
        
        Args:
            base_dir: 基础目录，所有文档处理都在此目录下
        """
        self.base_dir = Path(base_dir)
        self.base_dir.mkdir(exist_ok=True)
        
        # 支持的文档类型
        self.supported_types = {
            '.pdf': 'pdf',
            '.docx': 'word',
            '.doc': 'word',
            '.txt': 'text',
            '.md': 'markdown'
        }
        
        # 创建标准子目录
        self.subdirs = {
            'source': self.base_dir / 'source',           # 原始文档
            'extracted': self.base_dir / 'extracted',     # 提取的内容
            'images': self.base_dir / 'images',           # 图片资源
            'anki': self.base_dir / 'anki',               # 生成的anki卡片
            'cache': self.base_dir / 'cache',             # 缓存文件
            'logs': self.base_dir / 'logs'                # 日志文件
        }
        
        # 创建所有子目录
        for subdir in self.subdirs.values():
            subdir.mkdir(exist_ok=True)
    
    def get_file_info(self, file_path: str) -> Dict[str, str]:
        """
        获取文件信息
        
        Args:
            file_path: 文件路径
            
        Returns:
            包含文件信息的字典
        """
        path = Path(file_path)
        ext = path.suffix.lower()
        
        info = {
            'original_path': str(path.absolute()),
            'filename': path.stem,
            'extension': ext,
            'type': self.supported_types.get(ext, 'unknown'),
            'size': path.stat().st_size if path.exists() else 0,
            'timestamp': datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
        }
        
        # 生成标准化文件名（去除特殊字符）
        clean_name = re.sub(r'[^\w\-_\.]', '_', info['filename'])
        clean_name = re.sub(r'_+', '_', clean_name).strip('_')
        info['clean_name'] = clean_name
        
        return info
    
    def generate_project_name(self, file_info: Dict[str, str]) -> str:
        """
        为文档生成项目名称
        
        Args:
            file_info: 文件信息字典
            
        Returns:
            标准化的项目名称
        """
        return f"{file_info['clean_name']}_{file_info['timestamp']}"
    
    def get_extraction_paths(self, file_path: str) -> Dict[str, Path]:
        """
        获取文档提取相关的所有路径
        
        Args:
            file_path: 原始文件路径
            
        Returns:
            包含各种路径的字典
        """
        file_info = self.get_file_info(file_path)
        project_name = self.generate_project_name(file_info)
        
        # 为每个文档创建独立的提取目录
        extraction_dir = self.subdirs['extracted'] / project_name
        extraction_dir.mkdir(exist_ok=True)

        # 使用新的图像文件夹命名约定
        from document_utils import ImageFolderManager
        image_manager = ImageFolderManager()
        image_folder_name = image_manager.generate_image_folder_name(file_info['clean_name'])

        # 图片目录 - 使用新的命名约定
        images_dir = extraction_dir / image_folder_name
        images_dir.mkdir(exist_ok=True)
        
        # 缓存目录
        cache_dir = self.subdirs['cache'] / project_name
        cache_dir.mkdir(exist_ok=True)
        
        paths = {
            'project_name': project_name,
            'extraction_dir': extraction_dir,
            'markdown_file': extraction_dir / f"{file_info['clean_name']}.md",
            'images_dir': images_dir,
            'cache_dir': cache_dir,
            'anki_file': self.subdirs['anki'] / f"{project_name}_flashcards.txt",
            'log_file': self.subdirs['logs'] / f"{project_name}.log"
        }
        
        return paths
    
    def get_image_name(self, project_name: str, page_num: int, img_index: int,
                      source_type: str = 'page', use_simplified_naming: bool = True) -> str:
        """
        生成标准化的图片文件名

        Args:
            project_name: 项目名称
            page_num: 页码
            img_index: 图片索引
            source_type: 来源类型 (page, inline, etc.)
            use_simplified_naming: 是否使用简化命名（不包含项目名称前缀）

        Returns:
            标准化的图片文件名
        """
        if use_simplified_naming:
            # 新的简化命名格式：page_650_img_2.png
            return f"{source_type}_{page_num}_img_{img_index}.png"
        else:
            # 原有的完整命名格式（向后兼容）
            return f"{project_name}_{source_type}_{page_num:03d}_img_{img_index:03d}.png"
    
    def get_chunk_cache_path(self, project_name: str, chunk_index: int) -> Path:
        """
        获取分块缓存文件路径

        Args:
            project_name: 项目名称
            chunk_index: 分块索引

        Returns:
            缓存文件路径
        """
        cache_dir = self.subdirs['cache'] / project_name
        return cache_dir / f"chunk_{chunk_index:04d}.json"

    def _calculate_expected_chunks(self, markdown_file: Path, chunk_size: int = 500,
                                 chunk_stride: int = 450, lines_to_skip: int = 0) -> int:
        """
        计算markdown文件预期生成的chunk数量

        Args:
            markdown_file: markdown文件路径
            chunk_size: 每个chunk的行数
            chunk_stride: 滑动窗口步长
            lines_to_skip: 跳过文件开头的行数

        Returns:
            预期的chunk数量
        """
        if not markdown_file.exists():
            return 0

        try:
            with open(markdown_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            # 跳过指定的行数
            if lines_to_skip > 0:
                lines = lines[lines_to_skip:]

            if not lines:
                return 0

            # 使用与make_chunks相同的逻辑计算chunk数量
            chunk_count = 0
            start = 0

            while start < len(lines):
                end = min(start + chunk_size, len(lines))
                chunk_count += 1

                if end >= len(lines):
                    break

                start += chunk_stride

            return chunk_count

        except Exception as e:
            logging.warning(f"计算预期chunk数量时出错 {markdown_file}: {e}")
            return 0

    def _count_cache_files(self, cache_dir: Path) -> int:
        """
        计算缓存目录中的JSON文件数量

        Args:
            cache_dir: 缓存目录路径

        Returns:
            JSON缓存文件数量
        """
        if not cache_dir.exists():
            return 0

        try:
            # 计算符合命名模式的JSON文件：0.json, 1.json, 2.json, ...
            json_files = list(cache_dir.glob('*.json'))
            # 过滤出符合数字命名模式的文件
            valid_cache_files = []
            for json_file in json_files:
                try:
                    # 检查文件名是否为纯数字（不包括扩展名）
                    int(json_file.stem)
                    valid_cache_files.append(json_file)
                except ValueError:
                    # 跳过非数字命名的JSON文件
                    continue

            return len(valid_cache_files)

        except Exception as e:
            logging.warning(f"计算缓存文件数量时出错 {cache_dir}: {e}")
            return 0

    def get_cache_progress(self, project_name: str, chunk_size: int = 500,
                          chunk_stride: int = 450, lines_to_skip: int = 0) -> Dict[str, Union[int, float, str]]:
        """
        获取项目的缓存进度信息

        Args:
            project_name: 项目名称
            chunk_size: 每个chunk的行数
            chunk_stride: 滑动窗口步长
            lines_to_skip: 跳过文件开头的行数

        Returns:
            缓存进度信息字典
        """
        extraction_dir = self.subdirs['extracted'] / project_name
        cache_dir = self.subdirs['cache'] / project_name

        # 查找markdown文件
        markdown_file = None
        if extraction_dir.exists():
            md_files = list(extraction_dir.glob('*.md'))
            if md_files:
                markdown_file = sorted(md_files)[0]

        if not markdown_file:
            return {
                'cached': 0,
                'total': 0,
                'percentage': 0.0,
                'status': '无markdown文件'
            }

        # 计算预期chunk数量
        total_chunks = self._calculate_expected_chunks(markdown_file, chunk_size, chunk_stride, lines_to_skip)

        # 计算已缓存的文件数量
        cached_chunks = self._count_cache_files(cache_dir)

        # 计算百分比
        percentage = (cached_chunks / total_chunks * 100) if total_chunks > 0 else 0.0

        # 生成状态字符串
        if total_chunks == 0:
            status = '无需缓存'
        elif cached_chunks == 0:
            status = '未缓存'
        elif cached_chunks >= total_chunks:
            status = '已完成'
        else:
            status = f'{cached_chunks}/{total_chunks} ({percentage:.1f}%)'

        return {
            'cached': cached_chunks,
            'total': total_chunks,
            'percentage': percentage,
            'status': status
        }
    
    def list_projects(self) -> List[Dict[str, str]]:
        """
        列出所有处理过的项目
        
        Returns:
            项目信息列表
        """
        projects = []
        
        for extraction_dir in self.subdirs['extracted'].iterdir():
            if extraction_dir.is_dir():
                # 查找markdown文件
                md_files = list(extraction_dir.glob('*.md'))
                if md_files:
                    md_file = md_files[0]
                    # 查找图像文件夹（可能是 images 或重命名后的文件夹）
                    image_folders = [item for item in extraction_dir.iterdir()
                                   if item.is_dir() and 'images' in item.name.lower()]

                    has_images = len(image_folders) > 0
                    image_count = 0
                    if has_images:
                        for img_folder in image_folders:
                            image_count += len(list(img_folder.glob('*.png')))

                    # 获取缓存进度信息
                    cache_progress = self.get_cache_progress(extraction_dir.name)

                    project_info = {
                        'name': extraction_dir.name,
                        'path': str(extraction_dir),
                        'markdown_file': str(md_file),
                        'has_images': has_images,
                        'image_count': image_count,
                        'cache_progress': cache_progress,
                        'has_anki': (self.subdirs['anki'] / f"{extraction_dir.name}_flashcards.txt").exists()
                    }
                    projects.append(project_info)
        
        return sorted(projects, key=lambda x: x['name'])
    
    def cleanup_project(self, project_name: str, keep_anki: bool = True) -> bool:
        """
        清理项目文件
        
        Args:
            project_name: 项目名称
            keep_anki: 是否保留anki文件
            
        Returns:
            是否成功清理
        """
        try:
            # 删除提取目录
            extraction_dir = self.subdirs['extracted'] / project_name
            if extraction_dir.exists():
                import shutil
                shutil.rmtree(extraction_dir)
            
            # 删除缓存目录
            cache_dir = self.subdirs['cache'] / project_name
            if cache_dir.exists():
                import shutil
                shutil.rmtree(cache_dir)
            
            # 删除日志文件
            log_file = self.subdirs['logs'] / f"{project_name}.log"
            if log_file.exists():
                log_file.unlink()
            
            # 可选删除anki文件
            if not keep_anki:
                anki_file = self.subdirs['anki'] / f"{project_name}_flashcards.txt"
                if anki_file.exists():
                    anki_file.unlink()
            
            logging.info(f"项目 {project_name} 清理完成")
            return True
            
        except Exception as e:
            logging.error(f"清理项目 {project_name} 失败: {e}")
            return False
    
    def get_config_for_project(self, project_name: str) -> Dict[str, str]:
        """
        为项目生成配置信息

        Args:
            project_name: 项目名称

        Returns:
            项目配置字典
        """
        # 直接使用项目目录路径，不添加dummy前缀
        extraction_dir = self.subdirs['extracted'] / project_name
        cache_dir = self.subdirs['cache'] / project_name
        anki_file = self.subdirs['anki'] / f"{project_name}_flashcards.txt"

        # 查找项目中的markdown文件
        markdown_file = None
        if extraction_dir.exists():
            md_files = list(extraction_dir.glob('*.md'))
            if md_files:
                # 如果有多个markdown文件，选择第一个（按名称排序）
                markdown_file = sorted(md_files)[0]

        config = {
            'INPUT_FILE': str(markdown_file) if markdown_file else str(extraction_dir / f"{project_name}.md"),
            'OUTPUT_FILE': str(anki_file),
            'CACHE_DIR': str(cache_dir),
            'PROJECT_NAME': project_name,
            'EXTRACTION_DIR': str(extraction_dir)
        }

        return config

    def merge_adjacent_spans(self, html_content: str) -> str:
        """
        合并相邻的具有相同style属性的HTML span元素

        Enhancement 1: HTML Span Element Merging for Word-to-Markdown Conversion

        Args:
            html_content: 包含HTML span元素的内容

        Returns:
            合并后的HTML内容
        """
        if not html_content or not isinstance(html_content, str):
            return html_content or ""

        try:
            # 使用正则表达式匹配相邻的具有相同style属性的span元素
            # 模式: <span style="style1">content1</span>whitespace<span style="style1">content2</span>
            pattern = r'<span\s+style="([^"]+)">([^<]*)</span>(\s*)<span\s+style="\1">([^<]*)</span>'

            # 持续合并直到没有更多可合并的span
            previous_content = ""
            current_content = html_content
            merge_count = 0

            while current_content != previous_content and merge_count < 100:  # 防止无限循环
                previous_content = current_content
                # 替换匹配的模式，保留中间的空白字符
                current_content = re.sub(pattern, r'<span style="\1">\2\3\4</span>', current_content)
                merge_count += 1

            if merge_count > 0:
                logging.debug(f"HTML span合并完成，执行了 {merge_count} 次合并操作")

            return current_content

        except Exception as e:
            logging.warning(f"HTML span合并过程中出错: {e}")
            return html_content  # 出错时返回原始内容

    def fix_image_paths_in_markdown(self, markdown_content: str, document_name: str) -> str:
        """
        修复Markdown文件中的图片路径格式

        Enhancement 2: Fix Image Path References in Generated Markdown Files
        将 images/filename 格式修正为 images_{document_name}/filename 格式

        Args:
            markdown_content: Markdown内容
            document_name: 文档名称（用于生成正确的图片文件夹名称）

        Returns:
            修复后的Markdown内容
        """
        if not markdown_content or not isinstance(markdown_content, str):
            return markdown_content or ""

        if not document_name:
            logging.warning("文档名称为空，无法修复图片路径")
            return markdown_content

        try:
            # 使用document_utils中的工具生成正确的图片文件夹名称
            from document_utils import ImageFolderManager, DocumentNameCleaner

            name_cleaner = DocumentNameCleaner()
            image_manager = ImageFolderManager(name_cleaner)

            # 生成正确的图片文件夹名称
            clean_doc_name = name_cleaner.clean_document_name(document_name)
            correct_folder_name = image_manager.generate_image_folder_name(clean_doc_name)

            # 查找并替换错误的图片路径格式
            # 匹配模式: src="images/filename" 或 src='images/filename'
            pattern_double_quote = r'src="images/([^"]+)"'
            pattern_single_quote = r"src='images/([^']+)'"

            # 替换双引号格式
            fixed_content = re.sub(
                pattern_double_quote,
                f'src="{correct_folder_name}/\\1"',
                markdown_content
            )

            # 替换单引号格式
            fixed_content = re.sub(
                pattern_single_quote,
                f"src='{correct_folder_name}/\\1'",
                fixed_content
            )

            # 统计修复的数量
            double_quote_matches = len(re.findall(pattern_double_quote, markdown_content))
            single_quote_matches = len(re.findall(pattern_single_quote, markdown_content))
            total_fixes = double_quote_matches + single_quote_matches

            if total_fixes > 0:
                logging.info(f"图片路径修复完成，共修复 {total_fixes} 个路径")
                logging.debug(f"使用的图片文件夹名称: {correct_folder_name}")

            return fixed_content

        except Exception as e:
            logging.warning(f"修复图片路径过程中出错: {e}")
            return markdown_content  # 出错时返回原始内容


def main():
    """测试文件管理器功能"""
    # 创建文件管理器实例
    fm = FileManager()
    
    # 测试文件信息获取
    test_files = ['test.pdf', 'document.docx', 'notes.txt']
    
    for file_path in test_files:
        print(f"\n文件: {file_path}")
        file_info = fm.get_file_info(file_path)
        print(f"  信息: {file_info}")
        
        paths = fm.get_extraction_paths(file_path)
        print(f"  路径: {paths}")
    
    # 列出项目
    projects = fm.list_projects()
    print(f"\n现有项目: {len(projects)} 个")
    for project in projects:
        print(f"  - {project}")


if __name__ == "__main__":
    main()
