#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
failed_block_logger.py

失败块日志记录器 - 管理AI处理失败的文本块
提供结构化的失败记录、查询和重试功能
"""

import json
import logging
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict


@dataclass
class FailedBlock:
    """失败块数据类"""
    timestamp: str
    block_index: int
    project_name: str
    input_content: str
    output_content: str
    failure_reason: str
    failure_type: str  # 'validation', 'api_error', 'network_error', 'parsing_error'
    attempt_count: int
    input_length: int
    output_length: int
    validation_ratio: float = 0.0
    channel_name: str = ""
    error_details: str = ""


class FailedBlockLogger:
    """
    失败块日志记录器
    
    管理AI处理失败的文本块，提供结构化的日志记录和查询功能
    """
    
    def __init__(self, log_file: str = "failed_blocks.log", max_content_length: int = 1000):
        """
        初始化失败块日志记录器
        
        Args:
            log_file: 日志文件路径
            max_content_length: 记录内容的最大长度（避免日志文件过大）
        """
        self.log_file = Path(log_file)
        self.max_content_length = max_content_length
        
        # 确保日志文件目录存在
        self.log_file.parent.mkdir(parents=True, exist_ok=True)
        
        logging.info(f"失败块日志记录器初始化: {self.log_file}")
    
    def log_failed_block(self, 
                        block_index: int,
                        project_name: str,
                        input_content: str,
                        output_content: str = "",
                        failure_reason: str = "",
                        failure_type: str = "unknown",
                        attempt_count: int = 1,
                        validation_ratio: float = 0.0,
                        channel_name: str = "",
                        error_details: str = "") -> None:
        """
        记录失败的块
        
        Args:
            block_index: 块索引
            project_name: 项目名称
            input_content: 输入内容
            output_content: 输出内容（如果有）
            failure_reason: 失败原因
            failure_type: 失败类型
            attempt_count: 尝试次数
            validation_ratio: 验证比例
            channel_name: 通道名称
            error_details: 错误详情
        """
        # 截断过长的内容
        truncated_input = self._truncate_content(input_content)
        truncated_output = self._truncate_content(output_content)
        
        failed_block = FailedBlock(
            timestamp=datetime.now().isoformat(),
            block_index=block_index,
            project_name=project_name,
            input_content=truncated_input,
            output_content=truncated_output,
            failure_reason=failure_reason,
            failure_type=failure_type,
            attempt_count=attempt_count,
            input_length=len(input_content),
            output_length=len(output_content),
            validation_ratio=validation_ratio,
            channel_name=channel_name,
            error_details=error_details
        )
        
        # 写入日志文件
        try:
            with open(self.log_file, 'a', encoding='utf-8') as f:
                json.dump(asdict(failed_block), f, ensure_ascii=False)
                f.write('\n')
            
            logging.warning(f"记录失败块: 项目={project_name}, 块={block_index}, 原因={failure_reason}")
            
        except Exception as e:
            logging.error(f"写入失败块日志失败: {e}")
    
    def get_failed_blocks(self, project_name: Optional[str] = None) -> List[FailedBlock]:
        """
        获取失败的块列表
        
        Args:
            project_name: 项目名称过滤（可选）
            
        Returns:
            List[FailedBlock]: 失败块列表
        """
        failed_blocks = []
        
        if not self.log_file.exists():
            return failed_blocks
        
        try:
            with open(self.log_file, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    if not line:
                        continue
                    
                    try:
                        data = json.loads(line)
                        failed_block = FailedBlock(**data)
                        
                        # 项目名称过滤
                        if project_name is None or failed_block.project_name == project_name:
                            failed_blocks.append(failed_block)
                            
                    except (json.JSONDecodeError, TypeError) as e:
                        logging.warning(f"解析失败块日志第{line_num}行失败: {e}")
                        continue
                        
        except Exception as e:
            logging.error(f"读取失败块日志失败: {e}")
        
        return failed_blocks
    
    def get_failed_blocks_by_type(self, failure_type: str, project_name: Optional[str] = None) -> List[FailedBlock]:
        """
        按失败类型获取失败块
        
        Args:
            failure_type: 失败类型
            project_name: 项目名称过滤（可选）
            
        Returns:
            List[FailedBlock]: 失败块列表
        """
        all_failed = self.get_failed_blocks(project_name)
        return [block for block in all_failed if block.failure_type == failure_type]
    
    def get_retry_candidates(self, project_name: Optional[str] = None) -> List[FailedBlock]:
        """
        获取可重试的失败块
        
        Args:
            project_name: 项目名称过滤（可选）
            
        Returns:
            List[FailedBlock]: 可重试的失败块列表
        """
        failed_blocks = self.get_failed_blocks(project_name)
        
        # 过滤可重试的块（通常是验证失败或临时错误）
        retry_types = ['validation', 'network_error', 'timeout', 'rate_limit']
        return [block for block in failed_blocks if block.failure_type in retry_types]
    
    def get_statistics(self, project_name: Optional[str] = None) -> Dict[str, Any]:
        """
        获取失败块统计信息
        
        Args:
            project_name: 项目名称过滤（可选）
            
        Returns:
            Dict: 统计信息
        """
        failed_blocks = self.get_failed_blocks(project_name)
        
        if not failed_blocks:
            return {
                "total_failed": 0,
                "by_type": {},
                "by_project": {},
                "retry_candidates": 0
            }
        
        # 按类型统计
        by_type = {}
        for block in failed_blocks:
            by_type[block.failure_type] = by_type.get(block.failure_type, 0) + 1
        
        # 按项目统计
        by_project = {}
        for block in failed_blocks:
            by_project[block.project_name] = by_project.get(block.project_name, 0) + 1
        
        # 可重试数量
        retry_candidates = len(self.get_retry_candidates(project_name))
        
        return {
            "total_failed": len(failed_blocks),
            "by_type": by_type,
            "by_project": by_project,
            "retry_candidates": retry_candidates,
            "log_file": str(self.log_file),
            "file_exists": self.log_file.exists()
        }
    
    def clear_log(self, project_name: Optional[str] = None) -> int:
        """
        清理日志文件
        
        Args:
            project_name: 项目名称过滤（可选，如果指定则只清理该项目的记录）
            
        Returns:
            int: 清理的记录数量
        """
        if not self.log_file.exists():
            return 0
        
        if project_name is None:
            # 清理整个文件
            try:
                removed_count = sum(1 for _ in open(self.log_file, 'r', encoding='utf-8'))
                self.log_file.unlink()
                logging.info(f"已清理失败块日志文件，删除 {removed_count} 条记录")
                return removed_count
            except Exception as e:
                logging.error(f"清理失败块日志失败: {e}")
                return 0
        else:
            # 只清理指定项目的记录
            all_blocks = self.get_failed_blocks()
            remaining_blocks = [block for block in all_blocks if block.project_name != project_name]
            removed_count = len(all_blocks) - len(remaining_blocks)
            
            # 重写文件
            try:
                with open(self.log_file, 'w', encoding='utf-8') as f:
                    for block in remaining_blocks:
                        json.dump(asdict(block), f, ensure_ascii=False)
                        f.write('\n')
                
                logging.info(f"已清理项目 {project_name} 的失败块记录，删除 {removed_count} 条记录")
                return removed_count
                
            except Exception as e:
                logging.error(f"清理项目失败块记录失败: {e}")
                return 0
    
    def _truncate_content(self, content: str) -> str:
        """截断过长的内容"""
        if len(content) <= self.max_content_length:
            return content
        
        truncated = content[:self.max_content_length]
        return truncated + f"... (截断，原长度: {len(content)})"


if __name__ == "__main__":
    # 测试代码
    logging.basicConfig(level=logging.INFO)
    
    # 创建日志记录器
    logger = FailedBlockLogger("test_failed_blocks.log")
    
    # 测试记录失败块
    logger.log_failed_block(
        block_index=1,
        project_name="test_project",
        input_content="这是测试输入内容" * 50,
        output_content="短输出",
        failure_reason="输出长度不足",
        failure_type="validation",
        validation_ratio=0.3
    )
    
    # 获取统计信息
    stats = logger.get_statistics()
    print(f"统计信息: {json.dumps(stats, ensure_ascii=False, indent=2)}")
    
    # 清理测试文件
    logger.clear_log()
