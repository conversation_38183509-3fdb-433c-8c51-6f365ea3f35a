#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
test_enhancements.py

测试文件管理器中的两个新增强功能：
1. HTML span元素合并
2. 图片路径修复
"""

import sys
import logging
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from file_manager import FileManager

def test_html_span_merging():
    """测试HTML span元素合并功能"""
    print("=" * 60)
    print("测试 Enhancement 1: HTML Span Element Merging")
    print("=" * 60)
    
    fm = FileManager()
    
    # 测试用例1: 相邻的相同style span
    test_case_1 = '<span style="color:rgb(51, 51, 51)">文本1</span> <span style="color:rgb(51, 51, 51)">文本2</span>'
    expected_1 = '<span style="color:rgb(51, 51, 51)">文本1 文本2</span>'
    result_1 = fm.merge_adjacent_spans(test_case_1)
    
    print(f"测试用例1:")
    print(f"输入: {test_case_1}")
    print(f"期望: {expected_1}")
    print(f"结果: {result_1}")
    print(f"通过: {result_1 == expected_1}")
    print()
    
    # 测试用例2: 多个连续的相同style span
    test_case_2 = '<span style="color:red">A</span><span style="color:red">B</span> <span style="color:red">C</span>'
    result_2 = fm.merge_adjacent_spans(test_case_2)
    
    print(f"测试用例2:")
    print(f"输入: {test_case_2}")
    print(f"结果: {result_2}")
    print()
    
    # 测试用例3: 不同style的span（不应合并）
    test_case_3 = '<span style="color:red">红色</span> <span style="color:blue">蓝色</span>'
    result_3 = fm.merge_adjacent_spans(test_case_3)
    
    print(f"测试用例3:")
    print(f"输入: {test_case_3}")
    print(f"结果: {result_3}")
    print(f"应该不变: {result_3 == test_case_3}")
    print()
    
    # 测试用例4: 复杂的混合情况
    test_case_4 = '''<span style="color:rgb(255, 0, 0)">红色1</span> <span style="color:rgb(255, 0, 0)">红色2</span> 普通文本 <span style="color:blue">蓝色</span>'''
    result_4 = fm.merge_adjacent_spans(test_case_4)
    
    print(f"测试用例4:")
    print(f"输入: {test_case_4}")
    print(f"结果: {result_4}")
    print()

def test_image_path_fixing():
    """测试图片路径修复功能"""
    print("=" * 60)
    print("测试 Enhancement 2: Fix Image Path References")
    print("=" * 60)
    
    fm = FileManager()
    
    # 测试用例1: 基本的图片路径修复
    document_name = "测试文档"
    test_markdown_1 = '''
# 标题

这是一些文本。

<img alt="undefined" src="images/page_1_img_1.png">

更多文本。

<img alt="图片" src="images/page_2_img_1.png">
'''
    
    result_1 = fm.fix_image_paths_in_markdown(test_markdown_1, document_name)
    
    print(f"测试用例1:")
    print(f"文档名称: {document_name}")
    print(f"输入Markdown:")
    print(test_markdown_1)
    print(f"修复后的Markdown:")
    print(result_1)
    print()
    
    # 测试用例2: 包含特殊字符的文档名称
    document_name_2 = "复杂文档名：包含特殊字符【测试】"
    test_markdown_2 = '<img src="images/test.png"> <img src=\'images/test2.jpg\'>'
    
    result_2 = fm.fix_image_paths_in_markdown(test_markdown_2, document_name_2)
    
    print(f"测试用例2:")
    print(f"文档名称: {document_name_2}")
    print(f"输入: {test_markdown_2}")
    print(f"结果: {result_2}")
    print()
    
    # 测试用例3: 已经正确的路径（不应改变）
    test_markdown_3 = '<img src="images_测试文档/page_1_img_1.png">'
    result_3 = fm.fix_image_paths_in_markdown(test_markdown_3, "测试文档")
    
    print(f"测试用例3:")
    print(f"输入: {test_markdown_3}")
    print(f"结果: {result_3}")
    print(f"应该不变: {result_3 == test_markdown_3}")
    print()

def test_integration():
    """测试集成场景"""
    print("=" * 60)
    print("测试集成场景")
    print("=" * 60)

    fm = FileManager()

    # 模拟Word文档转换后的内容
    document_name = "Word文档测试"
    content_with_spans_and_images = '''
# 文档标题

<span style="color:rgb(255, 0, 0)">这是</span> <span style="color:rgb(255, 0, 0)">红色文本</span>

<img alt="undefined" src="images/page_1_img_1.png">

<span style="color:blue">蓝色文本</span>

<img src="images/page_2_img_1.png">
'''

    # 先应用span合并
    step1_result = fm.merge_adjacent_spans(content_with_spans_and_images)
    print("步骤1 - Span合并后:")
    print(step1_result)
    print()

    # 再应用图片路径修复
    step2_result = fm.fix_image_paths_in_markdown(step1_result, document_name)
    print("步骤2 - 图片路径修复后:")
    print(step2_result)
    print()

def test_edge_cases():
    """测试边界情况"""
    print("=" * 60)
    print("测试边界情况")
    print("=" * 60)

    fm = FileManager()

    # 测试空内容
    print("测试1: 空内容")
    result1 = fm.merge_adjacent_spans("")
    result2 = fm.fix_image_paths_in_markdown("", "test")
    print(f"空span合并结果: '{result1}'")
    print(f"空图片路径修复结果: '{result2}'")
    print()

    # 测试None输入
    print("测试2: None输入")
    result3 = fm.merge_adjacent_spans(None)
    result4 = fm.fix_image_paths_in_markdown(None, "test")
    print(f"None span合并结果: '{result3}'")
    print(f"None图片路径修复结果: '{result4}'")
    print()

    # 测试嵌套span
    print("测试3: 嵌套span（不应合并）")
    nested_spans = '<span style="color:red"><span style="font-weight:bold">粗体红色</span></span>'
    result5 = fm.merge_adjacent_spans(nested_spans)
    print(f"输入: {nested_spans}")
    print(f"结果: {result5}")
    print(f"应该不变: {result5 == nested_spans}")
    print()

    # 测试复杂的图片路径
    print("测试4: 复杂图片路径")
    complex_markdown = '''
    <img src="images/page_1_img_1.png" alt="test">
    <img src="./images/page_2_img_1.jpg" alt="relative">
    <img src="/absolute/path/image.png" alt="absolute">
    <img src="https://example.com/image.png" alt="url">
    '''
    result6 = fm.fix_image_paths_in_markdown(complex_markdown, "测试文档")
    print(f"复杂路径修复结果:")
    print(result6)
    print()

def test_source_path_generation():
    """测试从源头生成正确路径的效果"""
    print("=" * 60)
    print("测试从源头生成正确路径")
    print("=" * 60)

    from document_utils import ImageFolderManager, DocumentNameCleaner

    # 模拟Word和PDF提取器中的路径生成逻辑
    document_names = [
        "测试文档",
        "复杂文档名：包含特殊字符【测试】",
        "Word Document with Spaces",
        "PDF文档_2024"
    ]

    name_cleaner = DocumentNameCleaner()
    image_manager = ImageFolderManager(name_cleaner)

    for doc_name in document_names:
        print(f"原始文档名: {doc_name}")
        clean_name = name_cleaner.clean_document_name(doc_name)
        folder_name = image_manager.generate_image_folder_name(clean_name)

        # 模拟生成的图片标签
        img_filename = "page_1_img_1.png"

        # Word格式 (HTML img标签)
        word_img_tag = f'<img alt="undefined" src="{folder_name}/{img_filename}">'

        # PDF格式 (Markdown图片)
        pdf_img_tag = f"![{img_filename}]({folder_name}/{img_filename})"

        print(f"  清理后名称: {clean_name}")
        print(f"  图片文件夹: {folder_name}")
        print(f"  Word图片标签: {word_img_tag}")
        print(f"  PDF图片标签: {pdf_img_tag}")
        print()

    print("✅ 从源头生成的路径都是正确的格式！")

def main():
    """主测试函数"""
    # 设置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s [%(levelname)s] %(message)s'
    )
    
    print("开始测试文件管理器增强功能")
    print()
    
    try:
        test_html_span_merging()
        test_image_path_fixing()
        test_integration()
        test_edge_cases()
        test_source_path_generation()

        print("=" * 60)
        print("所有测试完成！")
        print("=" * 60)
        
    except Exception as e:
        print(f"测试过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
